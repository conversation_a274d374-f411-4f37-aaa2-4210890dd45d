import { useState } from 'react'
import { Product } from '@/types'
import { Search, Plus, Package, AlertTriangle } from 'lucide-react'
import { clsx } from 'clsx'

interface ProductSelectorProps {
  products: Product[]
  onAddProduct: (product: Product, quantity: number, unitPrice: number, discount?: number) => void
}

export function ProductSelector({ products, onAddProduct }: ProductSelectorProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const [quantity, setQuantity] = useState(1)
  const [unitPrice, setUnitPrice] = useState(0)
  const [discount, setDiscount] = useState(0)

  const filteredProducts = products.filter(product =>
    product.isActive && (
      product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.sku.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (product.barcode && product.barcode.includes(searchQuery))
    )
  )

  const handleSelectProduct = (product: Product) => {
    setSelectedProduct(product)
    setUnitPrice(product.sellingPrice)
    setQuantity(1)
    setDiscount(0)
    setSearchQuery('')
  }

  const handleAddToInvoice = () => {
    if (!selectedProduct) return

    if (quantity > selectedProduct.currentStock) {
      alert(`الكمية المطلوبة (${quantity}) أكبر من المخزون المتاح (${selectedProduct.currentStock})`)
      return
    }

    onAddProduct(selectedProduct, quantity, unitPrice, discount)
    
    // Reset form
    setSelectedProduct(null)
    setQuantity(1)
    setUnitPrice(0)
    setDiscount(0)
  }

  const handleCancel = () => {
    setSelectedProduct(null)
    setQuantity(1)
    setUnitPrice(0)
    setDiscount(0)
  }

  const calculateItemTotal = () => {
    return (quantity * unitPrice) - discount
  }

  if (selectedProduct) {
    return (
      <div className="border border-primary-200 bg-primary-50 rounded-lg p-4">
        <div className="space-y-4">
          {/* Product Info */}
          <div className="flex items-center">
            <div className="h-10 w-10 bg-primary-600 rounded-full flex items-center justify-center ml-3">
              <Package className="h-5 w-5 text-white" />
            </div>
            <div className="flex-1">
              <h3 className="font-medium text-gray-900">{selectedProduct.name}</h3>
              <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-600">
                <span>SKU: {selectedProduct.sku}</span>
                <span>المخزون: {selectedProduct.currentStock} {selectedProduct.unit}</span>
                <span>السعر: {selectedProduct.sellingPrice.toLocaleString()} ر.س</span>
              </div>
              {selectedProduct.currentStock <= selectedProduct.minStock && (
                <div className="flex items-center text-sm text-orange-600 mt-1">
                  <AlertTriangle className="h-3 w-3 ml-1" />
                  مخزون منخفض
                </div>
              )}
            </div>
          </div>

          {/* Quantity and Price */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                الكمية
              </label>
              <input
                type="number"
                min="1"
                max={selectedProduct.currentStock}
                value={quantity}
                onChange={(e) => setQuantity(Number(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                سعر الوحدة (ر.س)
              </label>
              <input
                type="number"
                step="0.01"
                min="0"
                value={unitPrice}
                onChange={(e) => setUnitPrice(Number(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
          </div>

          {/* Discount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              الخصم (ر.س)
            </label>
            <input
              type="number"
              step="0.01"
              min="0"
              max={quantity * unitPrice}
              value={discount}
              onChange={(e) => setDiscount(Number(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>

          {/* Total */}
          <div className="bg-white rounded-lg p-3 border border-gray-200">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">إجمالي السطر:</span>
              <span className="font-semibold text-lg text-gray-900">
                {calculateItemTotal().toLocaleString()} ر.س
              </span>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-3 space-x-reverse">
            <button
              onClick={handleAddToInvoice}
              disabled={quantity <= 0 || unitPrice <= 0 || quantity > selectedProduct.currentStock}
              className="btn-primary flex-1"
            >
              إضافة للفاتورة
            </button>
            <button
              onClick={handleCancel}
              className="btn-secondary"
            >
              إلغاء
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Search */}
      <div className="relative">
        <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="block w-full pr-10 pl-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          placeholder="البحث عن منتج بالاسم أو SKU أو الباركود..."
        />
      </div>

      {/* Product List */}
      {searchQuery && (
        <div className="max-h-60 overflow-y-auto border border-gray-200 rounded-lg">
          {filteredProducts.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {filteredProducts.map((product) => (
                <button
                  key={product.id}
                  onClick={() => handleSelectProduct(product)}
                  disabled={product.currentStock === 0}
                  className={clsx(
                    'w-full text-right p-4 transition-colors',
                    product.currentStock === 0
                      ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                      : 'hover:bg-gray-50'
                  )}
                >
                  <div className="flex items-center">
                    <div className={clsx(
                      'h-8 w-8 rounded-full flex items-center justify-center ml-3',
                      product.currentStock === 0 ? 'bg-gray-200' : 'bg-primary-100'
                    )}>
                      <Package className={clsx(
                        'h-4 w-4',
                        product.currentStock === 0 ? 'text-gray-400' : 'text-primary-600'
                      )} />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-gray-900">{product.name}</h4>
                        <span className="font-semibold text-primary-600">
                          {product.sellingPrice.toLocaleString()} ر.س
                        </span>
                      </div>
                      <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-600">
                        <span>SKU: {product.sku}</span>
                        <span className={clsx(
                          'flex items-center',
                          product.currentStock === 0 ? 'text-red-600' :
                          product.currentStock <= product.minStock ? 'text-orange-600' :
                          'text-green-600'
                        )}>
                          {product.currentStock === 0 && <AlertTriangle className="h-3 w-3 ml-1" />}
                          المخزون: {product.currentStock} {product.unit}
                        </span>
                      </div>
                      {product.currentStock === 0 && (
                        <div className="text-sm text-red-600 mt-1">
                          نفد المخزون
                        </div>
                      )}
                      {product.currentStock > 0 && product.currentStock <= product.minStock && (
                        <div className="text-sm text-orange-600 mt-1">
                          مخزون منخفض
                        </div>
                      )}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          ) : (
            <div className="p-4 text-center text-gray-500">
              لا توجد نتائج للبحث "{searchQuery}"
            </div>
          )}
        </div>
      )}

      {/* Quick Product Selection */}
      {!searchQuery && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">المنتجات الأكثر مبيعاً</h4>
          <div className="grid grid-cols-1 gap-2">
            {products.filter(p => p.isActive && p.currentStock > 0).slice(0, 3).map((product) => (
              <button
                key={product.id}
                onClick={() => handleSelectProduct(product)}
                className="text-right p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="h-6 w-6 bg-primary-100 rounded-full flex items-center justify-center ml-2">
                      <Package className="h-3 w-3 text-primary-600" />
                    </div>
                    <div>
                      <span className="font-medium text-gray-900">{product.name}</span>
                      <div className="text-sm text-gray-500">
                        المخزون: {product.currentStock} {product.unit}
                      </div>
                    </div>
                  </div>
                  <span className="font-semibold text-primary-600">
                    {product.sellingPrice.toLocaleString()} ر.س
                  </span>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
