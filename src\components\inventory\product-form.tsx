'use client'

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Product, ProductForm as ProductFormType } from '@/types'
import { X, Save, Package, Upload } from 'lucide-react'
import { useState } from 'react'
import toast from 'react-hot-toast'

const productSchema = z.object({
  name: z.string().min(1, 'اسم المنتج مطلوب'),
  nameEn: z.string().optional(),
  description: z.string().optional(),
  sku: z.string().min(1, 'رمز المنتج (SKU) مطلوب'),
  barcode: z.string().optional(),
  categoryId: z.string().min(1, 'فئة المنتج مطلوبة'),
  unit: z.string().min(1, 'وحدة القياس مطلوبة'),
  costPrice: z.number().min(0, 'سعر التكلفة يجب أن يكون أكبر من أو يساوي صفر'),
  sellingPrice: z.number().min(0.01, 'سعر البيع يجب أن يكون أكبر من صفر'),
  minStock: z.number().min(0, 'الحد الأدنى للمخزون يجب أن يكون أكبر من أو يساوي صفر'),
  currentStock: z.number().min(0, 'المخزون الحالي يجب أن يكون أكبر من أو يساوي صفر'),
})

interface ProductFormProps {
  product?: Product | null
  categories: Array<{ id: string; name: string }>
  onSave: (data: ProductFormType) => void
  onClose: () => void
}

const units = [
  'قطعة',
  'كيلوجرام',
  'جرام',
  'لتر',
  'متر',
  'سنتيمتر',
  'صندوق',
  'كرتون',
  'عبوة',
  'زجاجة',
  'علبة',
  'حبة',
  'دزينة',
  'مجموعة'
]

export function ProductForm({ product, categories, onSave, onClose }: ProductFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [imagePreview, setImagePreview] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<ProductFormType>({
    resolver: zodResolver(productSchema),
    defaultValues: product ? {
      name: product.name,
      nameEn: product.nameEn || '',
      description: product.description || '',
      sku: product.sku,
      barcode: product.barcode || '',
      categoryId: product.category.id,
      unit: product.unit,
      costPrice: product.costPrice,
      sellingPrice: product.sellingPrice,
      minStock: product.minStock,
      currentStock: product.currentStock,
    } : {
      unit: 'قطعة',
      costPrice: 0,
      sellingPrice: 0,
      minStock: 0,
      currentStock: 0,
    }
  })

  const costPrice = watch('costPrice')
  const sellingPrice = watch('sellingPrice')
  const profit = sellingPrice - costPrice
  const profitMargin = costPrice > 0 ? ((profit / costPrice) * 100) : 0

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const generateSKU = () => {
    const timestamp = Date.now().toString().slice(-6)
    const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
    setValue('sku', `PRD-${timestamp}-${randomNum}`)
  }

  const onSubmit = async (data: ProductFormType) => {
    try {
      setIsLoading(true)
      
      const formData = {
        ...data,
        costPrice: Number(data.costPrice),
        sellingPrice: Number(data.sellingPrice),
        minStock: Number(data.minStock),
        currentStock: Number(data.currentStock)
      }
      
      await onSave(formData)
      toast.success(product ? 'تم تحديث المنتج بنجاح' : 'تم إضافة المنتج بنجاح')
    } catch (error) {
      toast.error('حدث خطأ أثناء حفظ البيانات')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <Package className="h-6 w-6 text-primary-600 ml-3" />
            <h2 className="text-xl font-semibold text-gray-900">
              {product ? 'تعديل المنتج' : 'منتج جديد'}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 p-2 rounded-lg hover:bg-gray-100"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
          {/* Product Image */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">صورة المنتج</h3>
            <div className="flex items-center space-x-6 space-x-reverse">
              <div className="relative">
                <div className="w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-white">
                  {imagePreview ? (
                    <img
                      src={imagePreview}
                      alt="Product Preview"
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ) : (
                    <div className="text-center">
                      <Package className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-500">صورة المنتج</p>
                    </div>
                  )}
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-2">رفع صورة جديدة</h4>
                <p className="text-sm text-gray-600 mb-4">
                  يُفضل أن تكون الصورة بصيغة PNG أو JPG وبحجم 400x400 بكسل
                </p>
                <label className="btn-secondary cursor-pointer inline-flex items-center">
                  <Upload className="h-4 w-4 ml-2" />
                  اختيار صورة
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                  />
                </label>
              </div>
            </div>
          </div>

          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="md:col-span-2">
              <h3 className="text-lg font-medium text-gray-900 mb-4">المعلومات الأساسية</h3>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                اسم المنتج (بالعربية) *
              </label>
              <input
                {...register('name')}
                type="text"
                className="input-field"
                placeholder="اسم المنتج"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                اسم المنتج (بالإنجليزية)
              </label>
              <input
                {...register('nameEn')}
                type="text"
                className="input-field"
                placeholder="Product Name"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                وصف المنتج
              </label>
              <textarea
                {...register('description')}
                rows={3}
                className="input-field"
                placeholder="وصف تفصيلي للمنتج..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                رمز المنتج (SKU) *
              </label>
              <div className="flex">
                <input
                  {...register('sku')}
                  type="text"
                  className="input-field rounded-l-none"
                  placeholder="PRD-001"
                />
                <button
                  type="button"
                  onClick={generateSKU}
                  className="px-3 py-2 border border-gray-300 border-r-0 rounded-l-lg bg-gray-50 text-gray-700 hover:bg-gray-100 text-sm"
                >
                  توليد
                </button>
              </div>
              {errors.sku && (
                <p className="mt-1 text-sm text-red-600">{errors.sku.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الباركود
              </label>
              <input
                {...register('barcode')}
                type="text"
                className="input-field"
                placeholder="1234567890123"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                فئة المنتج *
              </label>
              <select {...register('categoryId')} className="input-field">
                <option value="">اختر الفئة</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
              {errors.categoryId && (
                <p className="mt-1 text-sm text-red-600">{errors.categoryId.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                وحدة القياس *
              </label>
              <select {...register('unit')} className="input-field">
                {units.map(unit => (
                  <option key={unit} value={unit}>
                    {unit}
                  </option>
                ))}
              </select>
              {errors.unit && (
                <p className="mt-1 text-sm text-red-600">{errors.unit.message}</p>
              )}
            </div>
          </div>

          {/* Pricing */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="md:col-span-2">
              <h3 className="text-lg font-medium text-gray-900 mb-4">التسعير</h3>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                سعر التكلفة (ر.س) *
              </label>
              <input
                {...register('costPrice', { valueAsNumber: true })}
                type="number"
                step="0.01"
                min="0"
                className="input-field"
                placeholder="0.00"
              />
              {errors.costPrice && (
                <p className="mt-1 text-sm text-red-600">{errors.costPrice.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                سعر البيع (ر.س) *
              </label>
              <input
                {...register('sellingPrice', { valueAsNumber: true })}
                type="number"
                step="0.01"
                min="0"
                className="input-field"
                placeholder="0.00"
              />
              {errors.sellingPrice && (
                <p className="mt-1 text-sm text-red-600">{errors.sellingPrice.message}</p>
              )}
            </div>

            {/* Profit Calculation */}
            {(costPrice > 0 || sellingPrice > 0) && (
              <div className="md:col-span-2 bg-blue-50 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">حساب الربح</h4>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-blue-700">الربح:</span>
                    <span className="font-semibold text-blue-900 mr-2">
                      {profit.toLocaleString()} ر.س
                    </span>
                  </div>
                  <div>
                    <span className="text-blue-700">هامش الربح:</span>
                    <span className="font-semibold text-blue-900 mr-2">
                      {profitMargin.toFixed(1)}%
                    </span>
                  </div>
                  <div>
                    <span className="text-blue-700">نسبة الربح من البيع:</span>
                    <span className="font-semibold text-blue-900 mr-2">
                      {sellingPrice > 0 ? ((profit / sellingPrice) * 100).toFixed(1) : 0}%
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Inventory */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="md:col-span-2">
              <h3 className="text-lg font-medium text-gray-900 mb-4">إدارة المخزون</h3>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                المخزون الحالي *
              </label>
              <input
                {...register('currentStock', { valueAsNumber: true })}
                type="number"
                min="0"
                className="input-field"
                placeholder="0"
              />
              {errors.currentStock && (
                <p className="mt-1 text-sm text-red-600">{errors.currentStock.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الحد الأدنى للمخزون *
              </label>
              <input
                {...register('minStock', { valueAsNumber: true })}
                type="number"
                min="0"
                className="input-field"
                placeholder="0"
              />
              {errors.minStock && (
                <p className="mt-1 text-sm text-red-600">{errors.minStock.message}</p>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3 space-x-reverse pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary"
            >
              إلغاء
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="btn-primary flex items-center"
            >
              {isLoading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
              ) : (
                <Save className="h-4 w-4 ml-2" />
              )}
              {isLoading ? 'جاري الحفظ...' : (product ? 'تحديث' : 'حفظ')}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
