import { useState } from 'react'
import { Receipt, ReceiptType, PaymentMethod } from '@/types'
import { Edit, Trash2, Eye, Printer, Receipt as ReceiptIcon, CreditCard } from 'lucide-react'
import { clsx } from 'clsx'

interface ReceiptListProps {
  receipts: Receipt[]
  onEdit: (receipt: Receipt) => void
  onDelete: (id: string) => void
  onPrint: (id: string) => void
}

const paymentMethodLabels: Record<PaymentMethod, string> = {
  CASH: 'نقدي',
  CREDIT_CARD: 'بطاقة ائتمان',
  BANK_TRANSFER: 'تحويل بنكي',
  CHECK: 'شيك',
  CREDIT: 'آجل'
}

const receiptTypeLabels: Record<ReceiptType, string> = {
  RECEIPT: 'سند قبض',
  PAYMENT: 'سند دفع'
}

export function ReceiptList({ receipts, onEdit, onDelete, onPrint }: ReceiptListProps) {
  const [selectedReceipts, setSelectedReceipts] = useState<string[]>([])

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedReceipts(receipts.map(receipt => receipt.id))
    } else {
      setSelectedReceipts([])
    }
  }

  const handleSelectReceipt = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedReceipts(prev => [...prev, id])
    } else {
      setSelectedReceipts(prev => prev.filter(receiptId => receiptId !== id))
    }
  }

  const handleDeleteSelected = () => {
    if (window.confirm(`هل أنت متأكد من حذف ${selectedReceipts.length} سند؟`)) {
      selectedReceipts.forEach(id => onDelete(id))
      setSelectedReceipts([])
    }
  }

  if (receipts.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-md border border-gray-200 p-8">
        <div className="text-center">
          <ReceiptIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد سندات</h3>
          <p className="text-gray-500">لم يتم إنشاء أي سندات بعد</p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <input
              type="checkbox"
              checked={selectedReceipts.length === receipts.length}
              onChange={(e) => handleSelectAll(e.target.checked)}
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <span className="mr-3 text-sm text-gray-700">
              {selectedReceipts.length > 0 
                ? `تم تحديد ${selectedReceipts.length} سند`
                : `${receipts.length} سند`
              }
            </span>
          </div>
          {selectedReceipts.length > 0 && (
            <button
              onClick={handleDeleteSelected}
              className="btn-danger text-sm flex items-center"
            >
              <Trash2 className="h-4 w-4 ml-2" />
              حذف المحدد
            </button>
          )}
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                السند
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                النوع
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                من/إلى
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                المبلغ
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                طريقة الدفع
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                التاريخ
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                الإجراءات
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {receipts.map((receipt) => (
              <tr
                key={receipt.id}
                className={clsx(
                  'hover:bg-gray-50',
                  selectedReceipts.includes(receipt.id) && 'bg-primary-50'
                )}
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedReceipts.includes(receipt.id)}
                      onChange={(e) => handleSelectReceipt(receipt.id, e.target.checked)}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded ml-3"
                    />
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {receipt.receiptNumber}
                      </div>
                      <div className="text-sm text-gray-500">
                        {receipt.description}
                      </div>
                      {receipt.notes && (
                        <div className="text-xs text-gray-400 mt-1">
                          {receipt.notes}
                        </div>
                      )}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={clsx(
                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                    receipt.type === ReceiptType.RECEIPT 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  )}>
                    {receipt.type === ReceiptType.RECEIPT ? (
                      <ReceiptIcon className="h-3 w-3 ml-1" />
                    ) : (
                      <CreditCard className="h-3 w-3 ml-1" />
                    )}
                    {receiptTypeLabels[receipt.type]}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    {receipt.fromTo}
                  </div>
                  {receipt.relatedInvoiceId && (
                    <div className="text-xs text-gray-500">
                      مرتبط بفاتورة: {receipt.relatedInvoiceId}
                    </div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className={clsx(
                    'text-sm font-semibold',
                    receipt.type === ReceiptType.RECEIPT ? 'text-green-600' : 'text-red-600'
                  )}>
                    {receipt.type === ReceiptType.RECEIPT ? '+' : '-'}
                    {receipt.amount.toLocaleString()} ر.س
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    {paymentMethodLabels[receipt.paymentMethod]}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    {new Date(receipt.date).toLocaleDateString('ar-SA')}
                  </div>
                  <div className="text-xs text-gray-500">
                    {new Date(receipt.createdAt).toLocaleTimeString('ar-SA', {
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <button
                      onClick={() => console.log('View receipt:', receipt.id)}
                      className="text-gray-400 hover:text-gray-600 p-1 rounded"
                      title="عرض"
                    >
                      <Eye className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => onPrint(receipt.id)}
                      className="text-blue-600 hover:text-blue-900 p-1 rounded"
                      title="طباعة"
                    >
                      <Printer className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => onEdit(receipt)}
                      className="text-primary-600 hover:text-primary-900 p-1 rounded"
                      title="تعديل"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => {
                        if (window.confirm('هل أنت متأكد من حذف هذا السند؟')) {
                          onDelete(receipt.id)
                        }
                      }}
                      className="text-red-600 hover:text-red-900 p-1 rounded"
                      title="حذف"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Footer */}
      <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            <span className="font-semibold">
              إجمالي القبض: {receipts.filter(r => r.type === ReceiptType.RECEIPT).reduce((sum, r) => sum + r.amount, 0).toLocaleString()} ر.س
            </span>
            <span className="mx-4">|</span>
            <span className="font-semibold">
              إجمالي الدفع: {receipts.filter(r => r.type === ReceiptType.PAYMENT).reduce((sum, r) => sum + r.amount, 0).toLocaleString()} ر.س
            </span>
          </div>
          <div className="text-sm text-gray-500">
            عرض {receipts.length} من {receipts.length} سند
          </div>
        </div>
      </div>
    </div>
  )
}
