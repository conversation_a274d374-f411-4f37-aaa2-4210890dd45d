import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsBoolean } from 'class-validator';

export class CreateCategoryDto {
  @ApiProperty({ 
    description: 'اسم الفئة',
    example: 'أجهزة الكمبيوتر'
  })
  @IsString()
  @IsNotEmpty({ message: 'اسم الفئة مطلوب' })
  name: string;

  @ApiProperty({ 
    description: 'اسم الفئة بالإنجليزية',
    example: 'Computers',
    required: false
  })
  @IsString()
  @IsOptional()
  nameEn?: string;

  @ApiProperty({ 
    description: 'وصف الفئة',
    example: 'فئة تشمل جميع أجهزة الكمبيوتر واللابتوب',
    required: false
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ 
    description: 'معرف الفئة الأب',
    example: 'clm1234567890',
    required: false
  })
  @IsString()
  @IsOptional()
  parentId?: string;

  @ApiProperty({ 
    description: 'حالة التفعيل',
    example: true,
    default: true
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}
