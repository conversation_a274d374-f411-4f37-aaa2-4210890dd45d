import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';

@Injectable()
export class CategoriesService {
  constructor(private prisma: PrismaService) {}

  async create(createCategoryDto: CreateCategoryDto) {
    // التحقق من وجود الفئة الأب إذا تم تحديدها
    if (createCategoryDto.parentId) {
      const parentCategory = await this.prisma.productCategory.findUnique({
        where: { id: createCategoryDto.parentId },
      });

      if (!parentCategory) {
        throw new NotFoundException('الفئة الأب غير موجودة');
      }
    }

    return this.prisma.productCategory.create({
      data: createCategoryDto,
      include: {
        parent: true,
        children: true,
        _count: {
          select: {
            products: true,
          },
        },
      },
    });
  }

  async findAll(parentId?: string, isActive?: boolean) {
    const where: any = {};

    if (parentId !== undefined) {
      where.parentId = parentId === 'null' ? null : parentId;
    }

    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    return this.prisma.productCategory.findMany({
      where,
      include: {
        parent: true,
        children: true,
        _count: {
          select: {
            products: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async findOne(id: string) {
    const category = await this.prisma.productCategory.findUnique({
      where: { id },
      include: {
        parent: true,
        children: true,
        products: {
          where: { isActive: true },
          take: 10,
        },
        _count: {
          select: {
            products: true,
          },
        },
      },
    });

    if (!category) {
      throw new NotFoundException('الفئة غير موجودة');
    }

    return category;
  }

  async update(id: string, updateCategoryDto: UpdateCategoryDto) {
    const category = await this.findOne(id);

    // التحقق من عدم جعل الفئة أب لنفسها
    if (updateCategoryDto.parentId === id) {
      throw new ConflictException('لا يمكن جعل الفئة أب لنفسها');
    }

    // التحقق من وجود الفئة الأب الجديدة إذا تم تحديدها
    if (updateCategoryDto.parentId && updateCategoryDto.parentId !== category.parentId) {
      const parentCategory = await this.prisma.productCategory.findUnique({
        where: { id: updateCategoryDto.parentId },
      });

      if (!parentCategory) {
        throw new NotFoundException('الفئة الأب غير موجودة');
      }

      // التحقق من عدم إنشاء دورة في التسلسل الهرمي
      await this.checkCircularReference(id, updateCategoryDto.parentId);
    }

    return this.prisma.productCategory.update({
      where: { id },
      data: updateCategoryDto,
      include: {
        parent: true,
        children: true,
        _count: {
          select: {
            products: true,
          },
        },
      },
    });
  }

  async remove(id: string) {
    const category = await this.findOne(id);

    // التحقق من عدم وجود منتجات في هذه الفئة
    if (category._count.products > 0) {
      throw new ConflictException('لا يمكن حذف فئة تحتوي على منتجات');
    }

    // التحقق من عدم وجود فئات فرعية
    if (category.children.length > 0) {
      throw new ConflictException('لا يمكن حذف فئة تحتوي على فئات فرعية');
    }

    return this.prisma.productCategory.delete({
      where: { id },
    });
  }

  async toggleActive(id: string) {
    const category = await this.findOne(id);

    return this.prisma.productCategory.update({
      where: { id },
      data: { isActive: !category.isActive },
      include: {
        parent: true,
        children: true,
        _count: {
          select: {
            products: true,
          },
        },
      },
    });
  }

  async getTree() {
    const categories = await this.prisma.productCategory.findMany({
      where: { parentId: null, isActive: true },
      include: {
        children: {
          where: { isActive: true },
          include: {
            children: {
              where: { isActive: true },
              include: {
                _count: {
                  select: {
                    products: true,
                  },
                },
              },
            },
            _count: {
              select: {
                products: true,
              },
            },
          },
        },
        _count: {
          select: {
            products: true,
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });

    return categories;
  }

  private async checkCircularReference(categoryId: string, parentId: string): Promise<void> {
    let currentParentId = parentId;
    const visited = new Set<string>();

    while (currentParentId) {
      if (visited.has(currentParentId)) {
        throw new ConflictException('تم اكتشاف دورة في التسلسل الهرمي للفئات');
      }

      if (currentParentId === categoryId) {
        throw new ConflictException('لا يمكن جعل الفئة الفرعية أب للفئة الأصلية');
      }

      visited.add(currentParentId);

      const parent = await this.prisma.productCategory.findUnique({
        where: { id: currentParentId },
        select: { parentId: true },
      });

      currentParentId = parent?.parentId || null;
    }
  }
}
