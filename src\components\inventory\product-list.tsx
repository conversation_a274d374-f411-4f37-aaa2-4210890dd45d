import { useState } from 'react'
import { Product } from '@/types'
import { Edit, Trash2, Eye, Package, BarChart3, AlertTriangle } from 'lucide-react'
import { clsx } from 'clsx'
import Image from 'next/image'

interface ProductListProps {
  products: Product[]
  onEdit: (product: Product) => void
  onDelete: (id: string) => void
  onViewStock: (id: string) => void
}

export function ProductList({ products, onEdit, onDelete, onViewStock }: ProductListProps) {
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])
  const [viewMode, setViewMode] = useState<'table' | 'grid'>('table')

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProducts(products.map(product => product.id))
    } else {
      setSelectedProducts([])
    }
  }

  const handleSelectProduct = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedProducts(prev => [...prev, id])
    } else {
      setSelectedProducts(prev => prev.filter(productId => productId !== id))
    }
  }

  const handleDeleteSelected = () => {
    if (window.confirm(`هل أنت متأكد من حذف ${selectedProducts.length} منتج؟`)) {
      selectedProducts.forEach(id => onDelete(id))
      setSelectedProducts([])
    }
  }

  const getStockStatus = (product: Product) => {
    if (product.currentStock === 0) {
      return { label: 'نفد المخزون', className: 'bg-red-100 text-red-800', icon: AlertTriangle }
    } else if (product.currentStock <= product.minStock) {
      return { label: 'مخزون منخفض', className: 'bg-yellow-100 text-yellow-800', icon: AlertTriangle }
    }
    return { label: 'متوفر', className: 'bg-green-100 text-green-800', icon: Package }
  }

  if (products.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-md border border-gray-200 p-8">
        <div className="text-center">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد منتجات</h3>
          <p className="text-gray-500">لم يتم إضافة أي منتجات بعد</p>
        </div>
      </div>
    )
  }

  if (viewMode === 'grid') {
    return (
      <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <span className="text-sm text-gray-700">
              {selectedProducts.length > 0 
                ? `تم تحديد ${selectedProducts.length} منتج`
                : `${products.length} منتج`
              }
            </span>
          </div>
          <div className="flex items-center space-x-3 space-x-reverse">
            {selectedProducts.length > 0 && (
              <button
                onClick={handleDeleteSelected}
                className="btn-danger text-sm flex items-center"
              >
                <Trash2 className="h-4 w-4 ml-2" />
                حذف المحدد
              </button>
            )}
            <div className="flex border border-gray-300 rounded-lg">
              <button
                onClick={() => setViewMode('table')}
                className={clsx(
                  'px-3 py-1 text-sm rounded-r-lg',
                  viewMode === 'table' ? 'bg-primary-600 text-white' : 'bg-white text-gray-700'
                )}
              >
                جدول
              </button>
              <button
                onClick={() => setViewMode('grid')}
                className={clsx(
                  'px-3 py-1 text-sm rounded-l-lg',
                  viewMode === 'grid' ? 'bg-primary-600 text-white' : 'bg-white text-gray-700'
                )}
              >
                شبكة
              </button>
            </div>
          </div>
        </div>

        {/* Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {products.map((product) => {
            const status = getStockStatus(product)
            return (
              <div
                key={product.id}
                className={clsx(
                  'border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow',
                  selectedProducts.includes(product.id) && 'ring-2 ring-primary-500'
                )}
              >
                {/* Product Image */}
                <div className="aspect-square bg-gray-100 rounded-lg mb-4 flex items-center justify-center">
                  <Package className="h-12 w-12 text-gray-400" />
                </div>

                {/* Product Info */}
                <div className="space-y-2">
                  <div className="flex items-start justify-between">
                    <h3 className="font-medium text-gray-900 text-sm line-clamp-2">
                      {product.name}
                    </h3>
                    <input
                      type="checkbox"
                      checked={selectedProducts.includes(product.id)}
                      onChange={(e) => handleSelectProduct(product.id, e.target.checked)}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                  </div>

                  <p className="text-xs text-gray-500">{product.sku}</p>

                  <div className="flex items-center justify-between">
                    <span className="text-sm font-semibold text-gray-900">
                      {product.sellingPrice.toLocaleString()} ر.س
                    </span>
                    <span className={clsx(
                      'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                      status.className
                    )}>
                      <status.icon className="h-3 w-3 ml-1" />
                      {product.currentStock}
                    </span>
                  </div>

                  <div className="flex items-center justify-between pt-2 border-t border-gray-200">
                    <button
                      onClick={() => onEdit(product)}
                      className="text-primary-600 hover:text-primary-900 text-sm"
                    >
                      تعديل
                    </button>
                    <button
                      onClick={() => onViewStock(product.id)}
                      className="text-gray-600 hover:text-gray-900 text-sm"
                    >
                      المخزون
                    </button>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <input
              type="checkbox"
              checked={selectedProducts.length === products.length}
              onChange={(e) => handleSelectAll(e.target.checked)}
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <span className="mr-3 text-sm text-gray-700">
              {selectedProducts.length > 0 
                ? `تم تحديد ${selectedProducts.length} منتج`
                : `${products.length} منتج`
              }
            </span>
          </div>
          <div className="flex items-center space-x-3 space-x-reverse">
            {selectedProducts.length > 0 && (
              <button
                onClick={handleDeleteSelected}
                className="btn-danger text-sm flex items-center"
              >
                <Trash2 className="h-4 w-4 ml-2" />
                حذف المحدد
              </button>
            )}
            <div className="flex border border-gray-300 rounded-lg">
              <button
                onClick={() => setViewMode('table')}
                className={clsx(
                  'px-3 py-1 text-sm rounded-r-lg',
                  viewMode === 'table' ? 'bg-primary-600 text-white' : 'bg-white text-gray-700'
                )}
              >
                جدول
              </button>
              <button
                onClick={() => setViewMode('grid')}
                className={clsx(
                  'px-3 py-1 text-sm rounded-l-lg',
                  viewMode === 'grid' ? 'bg-primary-600 text-white' : 'bg-white text-gray-700'
                )}
              >
                شبكة
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                المنتج
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                الفئة
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                المخزون
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                السعر
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                الحالة
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                الإجراءات
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {products.map((product) => {
              const status = getStockStatus(product)
              return (
                <tr
                  key={product.id}
                  className={clsx(
                    'hover:bg-gray-50',
                    selectedProducts.includes(product.id) && 'bg-primary-50'
                  )}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedProducts.includes(product.id)}
                        onChange={(e) => handleSelectProduct(product.id, e.target.checked)}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded ml-3"
                      />
                      <div className="flex items-center">
                        <div className="h-10 w-10 bg-gray-100 rounded-lg flex items-center justify-center ml-3">
                          <Package className="h-5 w-5 text-gray-400" />
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {product.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            SKU: {product.sku}
                          </div>
                          {product.barcode && (
                            <div className="text-xs text-gray-400">
                              باركود: {product.barcode}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      {product.category.name}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {product.currentStock} {product.unit}
                    </div>
                    <div className="text-xs text-gray-500">
                      الحد الأدنى: {product.minStock}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-semibold text-gray-900">
                      {product.sellingPrice.toLocaleString()} ر.س
                    </div>
                    <div className="text-xs text-gray-500">
                      التكلفة: {product.costPrice.toLocaleString()} ر.س
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={clsx(
                      'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                      status.className
                    )}>
                      <status.icon className="h-3 w-3 ml-1" />
                      {status.label}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <button
                        onClick={() => console.log('View product:', product.id)}
                        className="text-gray-400 hover:text-gray-600 p-1 rounded"
                        title="عرض"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => onViewStock(product.id)}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded"
                        title="حركات المخزون"
                      >
                        <BarChart3 className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => onEdit(product)}
                        className="text-primary-600 hover:text-primary-900 p-1 rounded"
                        title="تعديل"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => {
                          if (window.confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
                            onDelete(product.id)
                          }
                        }}
                        className="text-red-600 hover:text-red-900 p-1 rounded"
                        title="حذف"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              )
            })}
          </tbody>
        </table>
      </div>

      {/* Footer */}
      <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            إجمالي قيمة المخزون: <span className="font-semibold">
              {products.reduce((sum, product) => sum + (product.currentStock * product.costPrice), 0).toLocaleString()} ر.س
            </span>
          </div>
          <div className="text-sm text-gray-500">
            عرض {products.length} من {products.length} منتج
          </div>
        </div>
      </div>
    </div>
  )
}
