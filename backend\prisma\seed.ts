import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 بدء إنشاء البيانات التجريبية...');

  // إنشاء إعدادات الشركة
  const company = await prisma.company.upsert({
    where: { id: 'company-1' },
    update: {},
    create: {
      id: 'company-1',
      name: 'شركة المبيعات والمخازن المتقدمة',
      nameEn: 'Advanced Sales & Warehouse Company',
      address: 'الرياض، المملكة العربية السعودية',
      phone: '+966112345678',
      email: '<EMAIL>',
      website: 'https://saleswarehouse.com',
      taxNumber: '*********',
      commercialRegister: '1010123456',
      currency: 'SAR',
      invoicePrefix: 'INV',
      receiptPrefix: 'REC',
      paymentPrefix: 'PAY',
    },
  });

  // إنشاء المستخدم الإداري
  const hashedPassword = await bcrypt.hash('admin123', 10);
  const adminUser = await prisma.user.upsert({
    where: { username: 'admin' },
    update: {},
    create: {
      username: 'admin',
      email: '<EMAIL>',
      password: hashedPassword,
      fullName: 'مدير النظام',
      role: 'ADMIN',
      permissions: JSON.stringify([
        'MANAGE_USERS', 'VIEW_USERS', 'MANAGE_INVENTORY', 'VIEW_INVENTORY',
        'ADD_PRODUCTS', 'EDIT_PRODUCTS', 'DELETE_PRODUCTS', 'MANAGE_SALES',
        'VIEW_SALES', 'CREATE_INVOICES', 'EDIT_INVOICES', 'DELETE_INVOICES',
        'PRINT_INVOICES', 'MANAGE_EXPENSES', 'MANAGE_REVENUES', 'MANAGE_RECEIPTS',
        'MANAGE_PAYMENTS', 'VIEW_FINANCIAL_REPORTS', 'VIEW_REPORTS', 'EXPORT_REPORTS',
        'MANAGE_COMPANY_SETTINGS', 'MANAGE_SUPPLIERS', 'VIEW_SUPPLIERS'
      ]),
      isActive: true,
    },
  });

  // إنشاء مستخدم مبيعات
  const salesUser = await prisma.user.upsert({
    where: { username: 'sales1' },
    update: {},
    create: {
      username: 'sales1',
      email: '<EMAIL>',
      password: hashedPassword,
      fullName: 'موظف المبيعات',
      role: 'SALES_EMPLOYEE',
      permissions: JSON.stringify([
        'VIEW_SALES', 'CREATE_INVOICES', 'VIEW_INVENTORY', 'VIEW_USERS'
      ]),
      isActive: true,
    },
  });

  // إنشاء فئات المنتجات
  const electronicsCategory = await prisma.productCategory.create({
    data: {
      name: 'الإلكترونيات',
      nameEn: 'Electronics',
      description: 'جميع الأجهزة الإلكترونية',
      isActive: true,
    },
  });

  const computersCategory = await prisma.productCategory.create({
    data: {
      name: 'أجهزة الكمبيوتر',
      nameEn: 'Computers',
      description: 'أجهزة الكمبيوتر واللابتوب',
      parentId: electronicsCategory.id,
      isActive: true,
    },
  });

  const phonesCategory = await prisma.productCategory.create({
    data: {
      name: 'الهواتف الذكية',
      nameEn: 'Smartphones',
      description: 'الهواتف الذكية والأجهزة اللوحية',
      parentId: electronicsCategory.id,
      isActive: true,
    },
  });

  // إنشاء منتجات تجريبية
  const products = await Promise.all([
    prisma.product.create({
      data: {
        name: 'لابتوب ديل XPS 13',
        nameEn: 'Dell XPS 13 Laptop',
        description: 'لابتوب ديل بمعالج Intel Core i7 وذاكرة 16GB',
        sku: 'DELL-XPS-13-001',
        barcode: '*********0123',
        categoryId: computersCategory.id,
        unit: 'قطعة',
        costPrice: 4500,
        sellingPrice: 5500,
        minStock: 5,
        currentStock: 15,
        isActive: true,
      },
    }),
    prisma.product.create({
      data: {
        name: 'آيفون 15 برو',
        nameEn: 'iPhone 15 Pro',
        description: 'آيفون 15 برو بذاكرة 256GB',
        sku: 'APPLE-IP15-PRO-256',
        barcode: '2345678901234',
        categoryId: phonesCategory.id,
        unit: 'قطعة',
        costPrice: 4000,
        sellingPrice: 4800,
        minStock: 3,
        currentStock: 8,
        isActive: true,
      },
    }),
    prisma.product.create({
      data: {
        name: 'سامسونج جالاكسي S24',
        nameEn: 'Samsung Galaxy S24',
        description: 'سامسونج جالاكسي S24 بذاكرة 512GB',
        sku: 'SAMSUNG-S24-512',
        barcode: '3456789012345',
        categoryId: phonesCategory.id,
        unit: 'قطعة',
        costPrice: 3200,
        sellingPrice: 3800,
        minStock: 5,
        currentStock: 12,
        isActive: true,
      },
    }),
  ]);

  // إنشاء عملاء تجريبيين
  const customers = await Promise.all([
    prisma.customer.create({
      data: {
        name: 'أحمد محمد العلي',
        phone: '+966501234567',
        email: '<EMAIL>',
        address: 'الرياض، حي النخيل',
        creditLimit: 10000,
        currentBalance: 0,
        isActive: true,
      },
    }),
    prisma.customer.create({
      data: {
        name: 'فاطمة سعد الأحمد',
        phone: '+966502345678',
        email: '<EMAIL>',
        address: 'جدة، حي الروضة',
        creditLimit: 15000,
        currentBalance: 500,
        isActive: true,
      },
    }),
    prisma.customer.create({
      data: {
        name: 'شركة التقنية المتقدمة',
        phone: '+966503456789',
        email: '<EMAIL>',
        address: 'الدمام، الحي التجاري',
        taxNumber: '*********',
        creditLimit: 50000,
        currentBalance: -2500,
        isActive: true,
      },
    }),
  ]);

  console.log('✅ تم إنشاء البيانات التجريبية بنجاح!');
  console.log(`📊 تم إنشاء:`);
  console.log(`   - شركة واحدة`);
  console.log(`   - ${2} مستخدم`);
  console.log(`   - ${3} فئة منتجات`);
  console.log(`   - ${products.length} منتج`);
  console.log(`   - ${customers.length} عميل`);
}

main()
  .catch((e) => {
    console.error('❌ خطأ في إنشاء البيانات التجريبية:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
