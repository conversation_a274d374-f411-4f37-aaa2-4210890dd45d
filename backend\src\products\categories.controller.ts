import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { CategoriesService } from './categories.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('فئات المنتجات')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('categories')
export class CategoriesController {
  constructor(private readonly categoriesService: CategoriesService) {}

  @ApiOperation({ summary: 'إنشاء فئة جديدة' })
  @ApiResponse({ status: 201, description: 'تم إنشاء الفئة بنجاح' })
  @ApiResponse({ status: 404, description: 'الفئة الأب غير موجودة' })
  @Post()
  create(@Body() createCategoryDto: CreateCategoryDto) {
    return this.categoriesService.create(createCategoryDto);
  }

  @ApiOperation({ summary: 'الحصول على جميع الفئات' })
  @ApiResponse({ status: 200, description: 'قائمة الفئات' })
  @ApiQuery({ name: 'parentId', required: false, description: 'معرف الفئة الأب' })
  @ApiQuery({ name: 'isActive', required: false, description: 'حالة التفعيل' })
  @Get()
  findAll(
    @Query('parentId') parentId?: string,
    @Query('isActive') isActive?: string,
  ) {
    const isActiveBoolean = isActive === 'true' ? true : isActive === 'false' ? false : undefined;
    return this.categoriesService.findAll(parentId, isActiveBoolean);
  }

  @ApiOperation({ summary: 'الحصول على شجرة الفئات' })
  @ApiResponse({ status: 200, description: 'شجرة الفئات الهرمية' })
  @Get('tree')
  getTree() {
    return this.categoriesService.getTree();
  }

  @ApiOperation({ summary: 'الحصول على فئة بالمعرف' })
  @ApiResponse({ status: 200, description: 'بيانات الفئة' })
  @ApiResponse({ status: 404, description: 'الفئة غير موجودة' })
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.categoriesService.findOne(id);
  }

  @ApiOperation({ summary: 'تحديث بيانات الفئة' })
  @ApiResponse({ status: 200, description: 'تم تحديث الفئة بنجاح' })
  @ApiResponse({ status: 404, description: 'الفئة غير موجودة' })
  @ApiResponse({ status: 409, description: 'تعارض في البيانات' })
  @Patch(':id')
  update(@Param('id') id: string, @Body() updateCategoryDto: UpdateCategoryDto) {
    return this.categoriesService.update(id, updateCategoryDto);
  }

  @ApiOperation({ summary: 'تفعيل/إلغاء تفعيل الفئة' })
  @ApiResponse({ status: 200, description: 'تم تغيير حالة الفئة بنجاح' })
  @ApiResponse({ status: 404, description: 'الفئة غير موجودة' })
  @Patch(':id/toggle-active')
  toggleActive(@Param('id') id: string) {
    return this.categoriesService.toggleActive(id);
  }

  @ApiOperation({ summary: 'حذف الفئة' })
  @ApiResponse({ status: 200, description: 'تم حذف الفئة بنجاح' })
  @ApiResponse({ status: 404, description: 'الفئة غير موجودة' })
  @ApiResponse({ status: 409, description: 'لا يمكن حذف فئة تحتوي على منتجات أو فئات فرعية' })
  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.categoriesService.remove(id);
  }
}
