import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';
import { PrismaModule } from './prisma/prisma.module';
// import { AuthModule } from './auth/auth.module';
// import { UsersModule } from './users/users.module';
// import { CompanyModule } from './company/company.module';
// import { ProductsModule } from './products/products.module';
// import { CustomersModule } from './customers/customers.module';
// import { SuppliersModule } from './suppliers/suppliers.module';
// import { InvoicesModule } from './invoices/invoices.module';
// import { FinanceModule } from './finance/finance.module';
// import { InventoryModule } from './inventory/inventory.module';
// import { ReportsModule } from './reports/reports.module';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    
    // Rate limiting
    ThrottlerModule.forRoot([
      {
        ttl: 60000, // 1 minute
        limit: 100, // 100 requests per minute
      },
    ]),

    // Database
    PrismaModule,

    // Feature modules (commented out until modules are created)
    // AuthModule,
    // UsersModule,
    // CompanyModule,
    // ProductsModule,
    // CustomersModule,
    // SuppliersModule,
    // InvoicesModule,
    // FinanceModule,
    // InventoryModule,
    // ReportsModule,
  ],
})
export class AppModule {}
