import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { CustomersService } from './customers.service';
import { CreateCustomerDto } from './dto/create-customer.dto';
import { UpdateCustomerDto } from './dto/update-customer.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('العملاء')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('customers')
export class CustomersController {
  constructor(private readonly customersService: CustomersService) {}

  @ApiOperation({ summary: 'إنشاء عميل جديد' })
  @ApiResponse({ status: 201, description: 'تم إنشاء العميل بنجاح' })
  @Post()
  create(@Body() createCustomerDto: CreateCustomerDto) {
    return this.customersService.create(createCustomerDto);
  }

  @ApiOperation({ summary: 'الحصول على جميع العملاء' })
  @ApiResponse({ status: 200, description: 'قائمة العملاء' })
  @ApiQuery({ name: 'isActive', required: false, description: 'حالة التفعيل' })
  @Get()
  findAll(@Query('isActive') isActive?: string) {
    const isActiveBoolean = isActive === 'true' ? true : isActive === 'false' ? false : undefined;
    return this.customersService.findAll(isActiveBoolean);
  }

  @ApiOperation({ summary: 'البحث في العملاء' })
  @ApiResponse({ status: 200, description: 'نتائج البحث' })
  @Get('search')
  search(@Query('q') query: string) {
    return this.customersService.search(query);
  }

  @ApiOperation({ summary: 'العملاء الذين لديهم أرصدة' })
  @ApiResponse({ status: 200, description: 'قائمة العملاء الذين لديهم أرصدة' })
  @Get('with-balance')
  getCustomersWithBalance() {
    return this.customersService.getCustomersWithBalance();
  }

  @ApiOperation({ summary: 'الحصول على عميل بالمعرف' })
  @ApiResponse({ status: 200, description: 'بيانات العميل' })
  @ApiResponse({ status: 404, description: 'العميل غير موجود' })
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.customersService.findOne(id);
  }

  @ApiOperation({ summary: 'تحديث بيانات العميل' })
  @ApiResponse({ status: 200, description: 'تم تحديث العميل بنجاح' })
  @ApiResponse({ status: 404, description: 'العميل غير موجود' })
  @Patch(':id')
  update(@Param('id') id: string, @Body() updateCustomerDto: UpdateCustomerDto) {
    return this.customersService.update(id, updateCustomerDto);
  }

  @ApiOperation({ summary: 'تفعيل/إلغاء تفعيل العميل' })
  @ApiResponse({ status: 200, description: 'تم تغيير حالة العميل بنجاح' })
  @ApiResponse({ status: 404, description: 'العميل غير موجود' })
  @Patch(':id/toggle-active')
  toggleActive(@Param('id') id: string) {
    return this.customersService.toggleActive(id);
  }

  @ApiOperation({ summary: 'تحديث رصيد العميل' })
  @ApiResponse({ status: 200, description: 'تم تحديث الرصيد بنجاح' })
  @ApiResponse({ status: 404, description: 'العميل غير موجود' })
  @Patch(':id/balance')
  updateBalance(
    @Param('id') id: string,
    @Body('amount') amount: number,
    @Body('operation') operation: 'add' | 'subtract',
  ) {
    return this.customersService.updateBalance(id, amount, operation);
  }

  @ApiOperation({ summary: 'حذف العميل' })
  @ApiResponse({ status: 200, description: 'تم حذف العميل بنجاح' })
  @ApiResponse({ status: 404, description: 'العميل غير موجود' })
  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.customersService.remove(id);
  }
}
