import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsEmail, IsOptional, IsNumber, IsBoolean, Min } from 'class-validator';

export class CreateCustomerDto {
  @ApiProperty({ 
    description: 'اسم العميل',
    example: 'أحمد محمد'
  })
  @IsString()
  @IsNotEmpty({ message: 'اسم العميل مطلوب' })
  name: string;

  @ApiProperty({ 
    description: 'رقم الهاتف',
    example: '+966501234567',
    required: false
  })
  @IsString()
  @IsOptional()
  phone?: string;

  @ApiProperty({ 
    description: 'البريد الإلكتروني',
    example: '<EMAIL>',
    required: false
  })
  @IsEmail({}, { message: 'البريد الإلكتروني غير صحيح' })
  @IsOptional()
  email?: string;

  @ApiProperty({ 
    description: 'العنوان',
    example: 'الرياض، المملكة العربية السعودية',
    required: false
  })
  @IsString()
  @IsOptional()
  address?: string;

  @ApiProperty({ 
    description: 'الرقم الضريبي',
    example: '*********',
    required: false
  })
  @IsString()
  @IsOptional()
  taxNumber?: string;

  @ApiProperty({ 
    description: 'الحد الائتماني',
    example: 10000.00,
    default: 0
  })
  @IsNumber({}, { message: 'الحد الائتماني يجب أن يكون رقم' })
  @Min(0, { message: 'الحد الائتماني يجب أن يكون أكبر من أو يساوي صفر' })
  @IsOptional()
  creditLimit?: number;

  @ApiProperty({ 
    description: 'الرصيد الحالي',
    example: 0,
    default: 0
  })
  @IsNumber({}, { message: 'الرصيد الحالي يجب أن يكون رقم' })
  @IsOptional()
  currentBalance?: number;

  @ApiProperty({ 
    description: 'حالة التفعيل',
    example: true,
    default: true
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}
