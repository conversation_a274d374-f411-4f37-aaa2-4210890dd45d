'use client'

import { useState } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { InvoiceForm } from '@/components/sales/invoice-form'
import { InvoicePreview } from '@/components/sales/invoice-preview'
import { CustomerSelector } from '@/components/sales/customer-selector'
import { ProductSelector } from '@/components/sales/product-selector'
import { ShoppingCart, User, Package, FileText, Save, Print } from 'lucide-react'
import { Customer, Product, InvoiceItemForm, PaymentMethod } from '@/types'
import toast from 'react-hot-toast'

export default function NewInvoicePage() {
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
  const [invoiceItems, setInvoiceItems] = useState<InvoiceItemForm[]>([])
  const [discountAmount, setDiscountAmount] = useState(0)
  const [taxAmount, setTaxAmount] = useState(0)
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>(PaymentMethod.CASH)
  const [notes, setNotes] = useState('')
  const [showPreview, setShowPreview] = useState(false)

  // Mock data - في التطبيق الحقيقي، ستأتي من API
  const customers: Customer[] = [
    {
      id: '1',
      name: 'أحمد محمد علي',
      phone: '+966501234567',
      email: '<EMAIL>',
      address: 'الرياض، حي النخيل',
      taxNumber: '*********',
      creditLimit: 10000,
      currentBalance: 2500,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: '2',
      name: 'فاطمة سالم',
      phone: '+966507654321',
      email: '<EMAIL>',
      address: 'جدة، حي الصفا',
      creditLimit: 15000,
      currentBalance: 0,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ]

  const products: Product[] = [
    {
      id: '1',
      name: 'لابتوب ديل XPS 13',
      nameEn: 'Dell XPS 13 Laptop',
      description: 'لابتوب عالي الأداء للأعمال والتصميم',
      sku: 'DELL-XPS-13-001',
      barcode: '*********0123',
      category: {
        id: '1',
        name: 'أجهزة الكمبيوتر',
        nameEn: 'Computers',
        isActive: true
      },
      unit: 'قطعة',
      costPrice: 3500,
      sellingPrice: 4200,
      minStock: 5,
      currentStock: 12,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: '2',
      name: 'ماوس لوجيتك MX Master 3',
      nameEn: 'Logitech MX Master 3 Mouse',
      description: 'ماوس لاسلكي متقدم للمحترفين',
      sku: 'LOG-MX3-001',
      barcode: '*********0124',
      category: {
        id: '2',
        name: 'ملحقات الكمبيوتر',
        nameEn: 'Computer Accessories',
        isActive: true
      },
      unit: 'قطعة',
      costPrice: 280,
      sellingPrice: 350,
      minStock: 10,
      currentStock: 25,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ]

  const handleAddProduct = (product: Product, quantity: number, unitPrice: number, discount: number = 0) => {
    const existingItemIndex = invoiceItems.findIndex(item => item.productId === product.id)
    
    if (existingItemIndex >= 0) {
      // تحديث الكمية للمنتج الموجود
      const updatedItems = [...invoiceItems]
      updatedItems[existingItemIndex] = {
        ...updatedItems[existingItemIndex],
        quantity: updatedItems[existingItemIndex].quantity + quantity,
        unitPrice,
        discount
      }
      setInvoiceItems(updatedItems)
    } else {
      // إضافة منتج جديد
      const newItem: InvoiceItemForm = {
        productId: product.id,
        quantity,
        unitPrice,
        discount
      }
      setInvoiceItems([...invoiceItems, newItem])
    }
    
    toast.success(`تم إضافة ${product.name} إلى الفاتورة`)
  }

  const handleRemoveProduct = (productId: string) => {
    setInvoiceItems(invoiceItems.filter(item => item.productId !== productId))
    toast.success('تم حذف المنتج من الفاتورة')
  }

  const handleUpdateQuantity = (productId: string, quantity: number) => {
    if (quantity <= 0) {
      handleRemoveProduct(productId)
      return
    }

    const updatedItems = invoiceItems.map(item =>
      item.productId === productId ? { ...item, quantity } : item
    )
    setInvoiceItems(updatedItems)
  }

  const handleUpdatePrice = (productId: string, unitPrice: number) => {
    const updatedItems = invoiceItems.map(item =>
      item.productId === productId ? { ...item, unitPrice } : item
    )
    setInvoiceItems(updatedItems)
  }

  const handleUpdateDiscount = (productId: string, discount: number) => {
    const updatedItems = invoiceItems.map(item =>
      item.productId === productId ? { ...item, discount } : item
    )
    setInvoiceItems(updatedItems)
  }

  const calculateSubtotal = () => {
    return invoiceItems.reduce((sum, item) => {
      const itemTotal = (item.quantity * item.unitPrice) - item.discount
      return sum + itemTotal
    }, 0)
  }

  const calculateTotal = () => {
    const subtotal = calculateSubtotal()
    return subtotal + taxAmount - discountAmount
  }

  const handleSaveInvoice = async (isDraft: boolean = false) => {
    if (!selectedCustomer) {
      toast.error('يرجى اختيار العميل')
      return
    }

    if (invoiceItems.length === 0) {
      toast.error('يرجى إضافة منتجات للفاتورة')
      return
    }

    const invoiceData = {
      customerId: selectedCustomer.id,
      items: invoiceItems,
      subtotal: calculateSubtotal(),
      taxAmount,
      discountAmount,
      total: calculateTotal(),
      paymentMethod,
      notes,
      status: isDraft ? 'DRAFT' : 'PENDING'
    }

    try {
      // هنا سيتم إرسال البيانات إلى API
      console.log('Saving invoice:', invoiceData)
      
      toast.success(isDraft ? 'تم حفظ الفاتورة كمسودة' : 'تم إنشاء الفاتورة بنجاح')
      
      if (!isDraft) {
        setShowPreview(true)
      }
    } catch (error) {
      toast.error('حدث خطأ أثناء حفظ الفاتورة')
    }
  }

  const handlePrintInvoice = () => {
    // هنا سيتم تنفيذ طباعة الفاتورة
    window.print()
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">فاتورة جديدة</h1>
            <p className="text-gray-600 mt-1">إنشاء فاتورة مبيعات جديدة</p>
          </div>
          <div className="flex items-center space-x-3 space-x-reverse">
            <button
              onClick={() => handleSaveInvoice(true)}
              className="btn-secondary flex items-center"
              disabled={!selectedCustomer || invoiceItems.length === 0}
            >
              <Save className="h-4 w-4 ml-2" />
              حفظ كمسودة
            </button>
            <button
              onClick={() => setShowPreview(true)}
              className="btn-secondary flex items-center"
              disabled={!selectedCustomer || invoiceItems.length === 0}
            >
              <FileText className="h-4 w-4 ml-2" />
              معاينة
            </button>
            <button
              onClick={() => handleSaveInvoice(false)}
              className="btn-primary flex items-center"
              disabled={!selectedCustomer || invoiceItems.length === 0}
            >
              <ShoppingCart className="h-4 w-4 ml-2" />
              إنشاء الفاتورة
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Customer & Products */}
          <div className="lg:col-span-2 space-y-6">
            {/* Customer Selection */}
            <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
              <div className="flex items-center mb-4">
                <User className="h-5 w-5 text-primary-600 ml-2" />
                <h2 className="text-lg font-semibold text-gray-900">اختيار العميل</h2>
              </div>
              <CustomerSelector
                customers={customers}
                selectedCustomer={selectedCustomer}
                onSelectCustomer={setSelectedCustomer}
              />
            </div>

            {/* Product Selection */}
            <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
              <div className="flex items-center mb-4">
                <Package className="h-5 w-5 text-primary-600 ml-2" />
                <h2 className="text-lg font-semibold text-gray-900">إضافة المنتجات</h2>
              </div>
              <ProductSelector
                products={products}
                onAddProduct={handleAddProduct}
              />
            </div>
          </div>

          {/* Right Column - Invoice Details */}
          <div className="space-y-6">
            <InvoiceForm
              invoiceItems={invoiceItems}
              products={products}
              discountAmount={discountAmount}
              taxAmount={taxAmount}
              paymentMethod={paymentMethod}
              notes={notes}
              onUpdateQuantity={handleUpdateQuantity}
              onUpdatePrice={handleUpdatePrice}
              onUpdateDiscount={handleUpdateDiscount}
              onRemoveProduct={handleRemoveProduct}
              onDiscountChange={setDiscountAmount}
              onTaxChange={setTaxAmount}
              onPaymentMethodChange={setPaymentMethod}
              onNotesChange={setNotes}
              subtotal={calculateSubtotal()}
              total={calculateTotal()}
            />
          </div>
        </div>

        {/* Invoice Preview Modal */}
        {showPreview && selectedCustomer && (
          <InvoicePreview
            customer={selectedCustomer}
            items={invoiceItems}
            products={products}
            subtotal={calculateSubtotal()}
            taxAmount={taxAmount}
            discountAmount={discountAmount}
            total={calculateTotal()}
            paymentMethod={paymentMethod}
            notes={notes}
            onClose={() => setShowPreview(false)}
            onPrint={handlePrintInvoice}
          />
        )}
      </div>
    </DashboardLayout>
  )
}
