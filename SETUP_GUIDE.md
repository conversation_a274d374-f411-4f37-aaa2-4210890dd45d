# دليل تشغيل نظام إدارة المخازن والمبيعات

## نظرة عامة

تم إنشاء نظام شامل لإدارة المخازن والمبيعات والحسابات باستخدام أحدث التقنيات:

- **Frontend**: Next.js 14 مع TypeScript
- **Backend**: NestJS مع Prisma ORM
- **Desktop**: Electron
- **Database**: PostgreSQL
- **Styling**: Tailwind CSS

## الميزات المتوفرة حالياً

### ✅ تم تطويرها
1. **واجهة تسجيل الدخول** - تصميم احترافي مع دعم اللغة العربية
2. **لوحة التحكم الرئيسية** - إحصائيات ومؤشرات الأداء
3. **إعدادات الشركة** - إدارة بيانات الشركة والشعار
4. **إدارة المصروفات** - تسجيل وتتبع المصروفات
5. **سندات القبض والدفع** - إنشاء وإدارة السندات المالية
6. **إدارة المنتجات** - إضافة وتعديل المنتجات مع تتبع المخزون
7. **إنشاء الفواتير** - نظام فواتير متكامل مع معاينة وطباعة
8. **نظام الصلاحيات** - أدوار مختلفة للمستخدمين
9. **التصميم المتجاوب** - يعمل على جميع الأجهزة

### 🔄 قيد التطوير
- Backend API (NestJS)
- قاعدة البيانات (PostgreSQL + Prisma)
- تطبيق Electron
- التقارير المتقدمة
- إدارة العملاء والموردين

## كيفية التشغيل

### المتطلبات
- Node.js 18 أو أحدث
- npm أو yarn

### خطوات التشغيل

1. **تثبيت التبعيات**
```bash
npm install
```

2. **تشغيل التطبيق**
```bash
npm run dev
```

3. **فتح المتصفح**
```
http://localhost:3002
```

### بيانات تسجيل الدخول التجريبية
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## هيكل المشروع

```
sales_project_nextjs/
├── src/
│   ├── app/                    # صفحات Next.js
│   │   ├── dashboard/          # لوحة التحكم
│   │   ├── company-settings/   # إعدادات الشركة
│   │   ├── finance/           # الإدارة المالية
│   │   ├── inventory/         # إدارة المخزون
│   │   ├── sales/             # إدارة المبيعات
│   │   └── login/             # تسجيل الدخول
│   ├── components/            # مكونات واجهة المستخدم
│   │   ├── layout/            # مكونات التخطيط
│   │   ├── dashboard/         # مكونات لوحة التحكم
│   │   ├── finance/           # مكونات مالية
│   │   ├── inventory/         # مكونات المخزون
│   │   └── sales/             # مكونات المبيعات
│   ├── contexts/              # React Contexts
│   ├── lib/                   # مكتبات مساعدة
│   └── types/                 # تعريفات TypeScript
├── backend/                   # خادم NestJS
├── electron/                  # تطبيق Electron
└── assets/                    # الملفات الثابتة
```

## الصفحات المتاحة

### 🏠 الصفحات الأساسية
- `/` - الصفحة الرئيسية (إعادة توجيه)
- `/login` - تسجيل الدخول
- `/dashboard` - لوحة التحكم

### 🏢 إدارة الشركة
- `/company-settings` - إعدادات الشركة

### 💰 الإدارة المالية
- `/finance/expenses` - إدارة المصروفات
- `/finance/receipts` - سندات القبض والدفع

### 📦 إدارة المخزون
- `/inventory/products` - إدارة المنتجات

### 🛒 إدارة المبيعات
- `/sales/new-invoice` - إنشاء فاتورة جديدة

## المكونات الرئيسية

### 🎨 التصميم
- **Sidebar**: قائمة جانبية مع الروابط الرئيسية
- **Header**: شريط علوي مع البحث ومعلومات المستخدم
- **DashboardLayout**: تخطيط موحد لجميع الصفحات

### 📊 لوحة التحكم
- **StatsCard**: بطاقات الإحصائيات
- **SalesChart**: رسم بياني للمبيعات
- **RecentInvoices**: الفواتير الأخيرة
- **LowStockAlert**: تنبيهات المخزون

### 💸 المالية
- **ExpenseForm**: نموذج إضافة مصروف
- **ExpenseList**: قائمة المصروفات
- **ReceiptForm**: نموذج سند قبض/دفع
- **ReceiptList**: قائمة السندات

### 📦 المخزون
- **ProductForm**: نموذج إضافة/تعديل منتج
- **ProductList**: قائمة المنتجات
- **ProductStats**: إحصائيات المخزون

### 🧾 الفواتير
- **InvoiceForm**: نموذج الفاتورة
- **CustomerSelector**: اختيار العميل
- **ProductSelector**: اختيار المنتجات
- **InvoicePreview**: معاينة الفاتورة

## الميزات التقنية

### 🔐 الأمان
- نظام مصادقة JWT
- صلاحيات متدرجة
- حماية الصفحات

### 🎨 واجهة المستخدم
- تصميم متجاوب
- دعم كامل للغة العربية
- رسوم بيانية تفاعلية
- طباعة الفواتير

### 📱 التوافق
- يعمل على جميع المتصفحات الحديثة
- تصميم متجاوب للهواتف والأجهزة اللوحية
- دعم الطباعة

## الخطوات التالية

1. **إكمال Backend API**
   - إعداد قاعدة البيانات
   - تطوير APIs
   - ربط Frontend بـ Backend

2. **تطوير تطبيق Electron**
   - إعداد التطبيق
   - إضافة ميزات سطح المكتب

3. **إضافة ميزات متقدمة**
   - التقارير المفصلة
   - النسخ الاحتياطي
   - التصدير والاستيراد

4. **الاختبار والتحسين**
   - اختبارات الوحدة
   - اختبارات التكامل
   - تحسين الأداء

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- فتح issue في GitHub
- مراجعة الوثائق
- التواصل مع فريق التطوير

---

**ملاحظة**: هذا المشروع في مرحلة التطوير النشط. بعض الميزات قد تكون غير مكتملة أو تحتاج إلى تحسينات إضافية.
