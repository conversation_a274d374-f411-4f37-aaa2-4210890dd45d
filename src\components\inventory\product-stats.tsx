import { Product } from '@/types'
import { Package, AlertTriangle, TrendingDown, DollarSign } from 'lucide-react'

interface ProductStatsProps {
  products: Product[]
}

export function ProductStats({ products }: ProductStatsProps) {
  // حساب الإحصائيات
  const totalProducts = products.length
  const activeProducts = products.filter(p => p.isActive).length
  
  // المنتجات منخفضة المخزون
  const lowStockProducts = products.filter(p => 
    p.currentStock <= p.minStock && p.currentStock > 0
  ).length
  
  // المنتجات نافدة المخزون
  const outOfStockProducts = products.filter(p => p.currentStock === 0).length
  
  // إجمالي قيمة المخزون
  const totalInventoryValue = products.reduce((sum, product) => 
    sum + (product.currentStock * product.costPrice), 0
  )
  
  // متوسط سعر التكلفة
  const averageCostPrice = products.length > 0 
    ? products.reduce((sum, product) => sum + product.costPrice, 0) / products.length 
    : 0

  const stats = [
    {
      title: 'إجمالي المنتجات',
      value: totalProducts.toString(),
      unit: 'منتج',
      subValue: `${activeProducts} نشط`,
      icon: Package,
      color: 'bg-blue-500'
    },
    {
      title: 'مخزون منخفض',
      value: lowStockProducts.toString(),
      unit: 'منتج',
      subValue: 'يحتاج إعادة تموين',
      icon: AlertTriangle,
      color: 'bg-yellow-500'
    },
    {
      title: 'نفد المخزون',
      value: outOfStockProducts.toString(),
      unit: 'منتج',
      subValue: 'غير متوفر',
      icon: TrendingDown,
      color: 'bg-red-500'
    },
    {
      title: 'قيمة المخزون',
      value: totalInventoryValue.toLocaleString(),
      unit: 'ر.س',
      subValue: `متوسط التكلفة: ${averageCostPrice.toLocaleString()} ر.س`,
      icon: DollarSign,
      color: 'bg-green-500'
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat, index) => (
        <div key={index} className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
              <div className="flex items-baseline">
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                <p className="text-sm text-gray-500 mr-2">{stat.unit}</p>
              </div>
              <p className="text-sm text-gray-500 mt-1">{stat.subValue}</p>
            </div>
            <div className={`p-3 rounded-full ${stat.color}`}>
              <stat.icon className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
