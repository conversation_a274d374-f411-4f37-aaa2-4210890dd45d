import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsN<PERSON>ber, IsOptional, IsBoolean, Min } from 'class-validator';

export class CreateProductDto {
  @ApiProperty({ 
    description: 'اسم المنتج',
    example: 'لابتوب ديل'
  })
  @IsString()
  @IsNotEmpty({ message: 'اسم المنتج مطلوب' })
  name: string;

  @ApiProperty({ 
    description: 'اسم المنتج بالإنجليزية',
    example: 'Dell Laptop',
    required: false
  })
  @IsString()
  @IsOptional()
  nameEn?: string;

  @ApiProperty({ 
    description: 'وصف المنتج',
    example: 'لابتوب ديل بمعالج Intel Core i7',
    required: false
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ 
    description: 'رمز المنتج (SKU)',
    example: 'DELL-LAP-001'
  })
  @IsString()
  @IsNotEmpty({ message: 'رمز المنتج مطلوب' })
  sku: string;

  @ApiProperty({ 
    description: 'الباركود',
    example: '1234567890123',
    required: false
  })
  @IsString()
  @IsOptional()
  barcode?: string;

  @ApiProperty({ 
    description: 'معرف الفئة',
    example: 'clm1234567890'
  })
  @IsString()
  @IsNotEmpty({ message: 'فئة المنتج مطلوبة' })
  categoryId: string;

  @ApiProperty({ 
    description: 'وحدة القياس',
    example: 'قطعة'
  })
  @IsString()
  @IsNotEmpty({ message: 'وحدة القياس مطلوبة' })
  unit: string;

  @ApiProperty({ 
    description: 'سعر التكلفة',
    example: 2500.00
  })
  @IsNumber({}, { message: 'سعر التكلفة يجب أن يكون رقم' })
  @Min(0, { message: 'سعر التكلفة يجب أن يكون أكبر من أو يساوي صفر' })
  costPrice: number;

  @ApiProperty({ 
    description: 'سعر البيع',
    example: 3000.00
  })
  @IsNumber({}, { message: 'سعر البيع يجب أن يكون رقم' })
  @Min(0, { message: 'سعر البيع يجب أن يكون أكبر من أو يساوي صفر' })
  sellingPrice: number;

  @ApiProperty({ 
    description: 'الحد الأدنى للمخزون',
    example: 5,
    default: 0
  })
  @IsNumber({}, { message: 'الحد الأدنى للمخزون يجب أن يكون رقم' })
  @Min(0, { message: 'الحد الأدنى للمخزون يجب أن يكون أكبر من أو يساوي صفر' })
  @IsOptional()
  minStock?: number;

  @ApiProperty({ 
    description: 'المخزون الحالي',
    example: 10,
    default: 0
  })
  @IsNumber({}, { message: 'المخزون الحالي يجب أن يكون رقم' })
  @Min(0, { message: 'المخزون الحالي يجب أن يكون أكبر من أو يساوي صفر' })
  @IsOptional()
  currentStock?: number;

  @ApiProperty({ 
    description: 'حالة التفعيل',
    example: true,
    default: true
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}
