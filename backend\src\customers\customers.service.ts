import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateCustomerDto } from './dto/create-customer.dto';
import { UpdateCustomerDto } from './dto/update-customer.dto';

@Injectable()
export class CustomersService {
  constructor(private prisma: PrismaService) {}

  async create(createCustomerDto: CreateCustomerDto) {
    return this.prisma.customer.create({
      data: createCustomerDto,
    });
  }

  async findAll(isActive?: boolean) {
    const where: any = {};

    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    return this.prisma.customer.findMany({
      where,
      include: {
        _count: {
          select: {
            invoices: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async findOne(id: string) {
    const customer = await this.prisma.customer.findUnique({
      where: { id },
      include: {
        invoices: {
          orderBy: {
            createdAt: 'desc',
          },
          take: 10,
        },
        _count: {
          select: {
            invoices: true,
          },
        },
      },
    });

    if (!customer) {
      throw new NotFoundException('العميل غير موجود');
    }

    return customer;
  }

  async findByPhone(phone: string) {
    return this.prisma.customer.findFirst({
      where: { phone },
    });
  }

  async findByEmail(email: string) {
    return this.prisma.customer.findFirst({
      where: { email },
    });
  }

  async update(id: string, updateCustomerDto: UpdateCustomerDto) {
    await this.findOne(id);

    return this.prisma.customer.update({
      where: { id },
      data: updateCustomerDto,
    });
  }

  async remove(id: string) {
    const customer = await this.findOne(id);

    // التحقق من عدم وجود فواتير للعميل
    if (customer._count.invoices > 0) {
      throw new NotFoundException('لا يمكن حذف عميل له فواتير مسجلة');
    }

    return this.prisma.customer.delete({
      where: { id },
    });
  }

  async toggleActive(id: string) {
    const customer = await this.findOne(id);

    return this.prisma.customer.update({
      where: { id },
      data: { isActive: !customer.isActive },
    });
  }

  async updateBalance(id: string, amount: number, operation: 'add' | 'subtract') {
    const customer = await this.findOne(id);
    
    const newBalance = operation === 'add' 
      ? customer.currentBalance + amount 
      : customer.currentBalance - amount;

    return this.prisma.customer.update({
      where: { id },
      data: { currentBalance: newBalance },
    });
  }

  async getCustomersWithBalance() {
    return this.prisma.customer.findMany({
      where: {
        AND: [
          { isActive: true },
          { currentBalance: { not: 0 } },
        ],
      },
      orderBy: {
        currentBalance: 'desc',
      },
    });
  }

  async search(query: string) {
    return this.prisma.customer.findMany({
      where: {
        AND: [
          { isActive: true },
          {
            OR: [
              { name: { contains: query } },
              { phone: { contains: query } },
              { email: { contains: query } },
            ],
          },
        ],
      },
      take: 10,
    });
  }
}
