import { useState } from 'react'
import { Customer } from '@/types'
import { Search, Plus, User, Phone, MapPin, CreditCard } from 'lucide-react'
import { clsx } from 'clsx'

interface CustomerSelectorProps {
  customers: Customer[]
  selectedCustomer: Customer | null
  onSelectCustomer: (customer: Customer | null) => void
}

export function CustomerSelector({ customers, selectedCustomer, onSelectCustomer }: CustomerSelectorProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [showCustomerForm, setShowCustomerForm] = useState(false)

  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (customer.phone && customer.phone.includes(searchQuery)) ||
    (customer.email && customer.email.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  const handleSelectCustomer = (customer: Customer) => {
    onSelectCustomer(customer)
    setSearchQuery('')
  }

  const handleClearSelection = () => {
    onSelectCustomer(null)
  }

  if (selectedCustomer) {
    return (
      <div className="border border-primary-200 bg-primary-50 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="h-10 w-10 bg-primary-600 rounded-full flex items-center justify-center ml-3">
              <User className="h-5 w-5 text-white" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900">{selectedCustomer.name}</h3>
              <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-600">
                {selectedCustomer.phone && (
                  <div className="flex items-center">
                    <Phone className="h-3 w-3 ml-1" />
                    {selectedCustomer.phone}
                  </div>
                )}
                {selectedCustomer.address && (
                  <div className="flex items-center">
                    <MapPin className="h-3 w-3 ml-1" />
                    {selectedCustomer.address}
                  </div>
                )}
              </div>
              {selectedCustomer.currentBalance > 0 && (
                <div className="flex items-center text-sm text-orange-600 mt-1">
                  <CreditCard className="h-3 w-3 ml-1" />
                  رصيد مستحق: {selectedCustomer.currentBalance.toLocaleString()} ر.س
                </div>
              )}
            </div>
          </div>
          <button
            onClick={handleClearSelection}
            className="text-gray-400 hover:text-gray-600 text-sm"
          >
            تغيير
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Search */}
      <div className="relative">
        <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="block w-full pr-10 pl-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          placeholder="البحث عن عميل بالاسم أو الهاتف أو البريد الإلكتروني..."
        />
      </div>

      {/* Add New Customer Button */}
      <button
        onClick={() => setShowCustomerForm(true)}
        className="w-full flex items-center justify-center py-2 px-4 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-primary-300 hover:text-primary-600 transition-colors"
      >
        <Plus className="h-4 w-4 ml-2" />
        إضافة عميل جديد
      </button>

      {/* Customer List */}
      {searchQuery && (
        <div className="max-h-60 overflow-y-auto border border-gray-200 rounded-lg">
          {filteredCustomers.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {filteredCustomers.map((customer) => (
                <button
                  key={customer.id}
                  onClick={() => handleSelectCustomer(customer)}
                  className="w-full text-right p-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center">
                    <div className="h-8 w-8 bg-gray-200 rounded-full flex items-center justify-center ml-3">
                      <User className="h-4 w-4 text-gray-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{customer.name}</h4>
                      <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-600">
                        {customer.phone && (
                          <div className="flex items-center">
                            <Phone className="h-3 w-3 ml-1" />
                            {customer.phone}
                          </div>
                        )}
                        {customer.email && (
                          <span>{customer.email}</span>
                        )}
                      </div>
                      {customer.currentBalance > 0 && (
                        <div className="text-sm text-orange-600 mt-1">
                          رصيد مستحق: {customer.currentBalance.toLocaleString()} ر.س
                        </div>
                      )}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          ) : (
            <div className="p-4 text-center text-gray-500">
              لا توجد نتائج للبحث "{searchQuery}"
            </div>
          )}
        </div>
      )}

      {/* Quick Customer Selection */}
      {!searchQuery && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">العملاء الأخيرين</h4>
          <div className="space-y-1">
            {customers.slice(0, 3).map((customer) => (
              <button
                key={customer.id}
                onClick={() => handleSelectCustomer(customer)}
                className="w-full text-right p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center">
                  <div className="h-6 w-6 bg-gray-200 rounded-full flex items-center justify-center ml-2">
                    <User className="h-3 w-3 text-gray-600" />
                  </div>
                  <div>
                    <span className="font-medium text-gray-900">{customer.name}</span>
                    {customer.phone && (
                      <span className="text-sm text-gray-500 mr-2">
                        {customer.phone}
                      </span>
                    )}
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
