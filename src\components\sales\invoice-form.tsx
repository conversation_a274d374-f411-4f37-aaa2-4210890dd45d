import { Product, InvoiceItemForm, PaymentMethod } from '@/types'
import { Trash2, Edit, Calculator, CreditCard } from 'lucide-react'
import { useState } from 'react'

interface InvoiceFormProps {
  invoiceItems: InvoiceItemForm[]
  products: Product[]
  discountAmount: number
  taxAmount: number
  paymentMethod: PaymentMethod
  notes: string
  onUpdateQuantity: (productId: string, quantity: number) => void
  onUpdatePrice: (productId: string, unitPrice: number) => void
  onUpdateDiscount: (productId: string, discount: number) => void
  onRemoveProduct: (productId: string) => void
  onDiscountChange: (discount: number) => void
  onTaxChange: (tax: number) => void
  onPaymentMethodChange: (method: PaymentMethod) => void
  onNotesChange: (notes: string) => void
  subtotal: number
  total: number
}

const paymentMethodOptions = [
  { value: PaymentMethod.CASH, label: 'نقدي' },
  { value: PaymentMethod.CREDIT_CARD, label: 'بطاقة ائتمان' },
  { value: PaymentMethod.BANK_TRANSFER, label: 'تحويل بنكي' },
  { value: PaymentMethod.CHECK, label: 'شيك' },
  { value: PaymentMethod.CREDIT, label: 'آجل' },
]

export function InvoiceForm({
  invoiceItems,
  products,
  discountAmount,
  taxAmount,
  paymentMethod,
  notes,
  onUpdateQuantity,
  onUpdatePrice,
  onUpdateDiscount,
  onRemoveProduct,
  onDiscountChange,
  onTaxChange,
  onPaymentMethodChange,
  onNotesChange,
  subtotal,
  total
}: InvoiceFormProps) {
  const [editingItem, setEditingItem] = useState<string | null>(null)

  const getProductById = (productId: string) => {
    return products.find(p => p.id === productId)
  }

  const handleItemEdit = (productId: string) => {
    setEditingItem(editingItem === productId ? null : productId)
  }

  const calculateItemTotal = (item: InvoiceItemForm) => {
    return (item.quantity * item.unitPrice) - item.discount
  }

  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
      <div className="flex items-center mb-6">
        <Calculator className="h-5 w-5 text-primary-600 ml-2" />
        <h2 className="text-lg font-semibold text-gray-900">تفاصيل الفاتورة</h2>
      </div>

      {/* Invoice Items */}
      <div className="space-y-4 mb-6">
        {invoiceItems.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            لم يتم إضافة أي منتجات بعد
          </div>
        ) : (
          invoiceItems.map((item) => {
            const product = getProductById(item.productId)
            if (!product) return null

            const isEditing = editingItem === item.productId

            return (
              <div key={item.productId} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{product.name}</h4>
                    <p className="text-sm text-gray-500">SKU: {product.sku}</p>
                  </div>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <button
                      onClick={() => handleItemEdit(item.productId)}
                      className="text-primary-600 hover:text-primary-900 p-1 rounded"
                      title="تعديل"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => onRemoveProduct(item.productId)}
                      className="text-red-600 hover:text-red-900 p-1 rounded"
                      title="حذف"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                {isEditing ? (
                  <div className="grid grid-cols-3 gap-3">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        الكمية
                      </label>
                      <input
                        type="number"
                        min="1"
                        max={product.currentStock}
                        value={item.quantity}
                        onChange={(e) => onUpdateQuantity(item.productId, Number(e.target.value))}
                        className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-primary-500"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        سعر الوحدة
                      </label>
                      <input
                        type="number"
                        step="0.01"
                        min="0"
                        value={item.unitPrice}
                        onChange={(e) => onUpdatePrice(item.productId, Number(e.target.value))}
                        className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-primary-500"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        الخصم
                      </label>
                      <input
                        type="number"
                        step="0.01"
                        min="0"
                        max={item.quantity * item.unitPrice}
                        value={item.discount}
                        onChange={(e) => onUpdateDiscount(item.productId, Number(e.target.value))}
                        className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-primary-500"
                      />
                    </div>
                  </div>
                ) : (
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">الكمية:</span>
                      <span className="font-medium mr-2">{item.quantity} {product.unit}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">سعر الوحدة:</span>
                      <span className="font-medium mr-2">{item.unitPrice.toLocaleString()} ر.س</span>
                    </div>
                    <div>
                      <span className="text-gray-600">الخصم:</span>
                      <span className="font-medium mr-2">{item.discount.toLocaleString()} ر.س</span>
                    </div>
                  </div>
                )}

                <div className="mt-3 pt-3 border-t border-gray-200 flex justify-between items-center">
                  <span className="text-sm text-gray-600">إجمالي السطر:</span>
                  <span className="font-semibold text-gray-900">
                    {calculateItemTotal(item).toLocaleString()} ر.س
                  </span>
                </div>
              </div>
            )
          })
        )}
      </div>

      {/* Invoice Totals */}
      {invoiceItems.length > 0 && (
        <div className="space-y-4 border-t border-gray-200 pt-6">
          {/* Subtotal */}
          <div className="flex justify-between items-center">
            <span className="text-gray-600">المجموع الفرعي:</span>
            <span className="font-medium text-gray-900">
              {subtotal.toLocaleString()} ر.س
            </span>
          </div>

          {/* Discount */}
          <div className="flex justify-between items-center">
            <label className="text-gray-600">خصم إضافي:</label>
            <div className="flex items-center">
              <input
                type="number"
                step="0.01"
                min="0"
                max={subtotal}
                value={discountAmount}
                onChange={(e) => onDiscountChange(Number(e.target.value))}
                className="w-24 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-primary-500 ml-2"
              />
              <span className="text-sm text-gray-600">ر.س</span>
            </div>
          </div>

          {/* Tax */}
          <div className="flex justify-between items-center">
            <label className="text-gray-600">الضريبة:</label>
            <div className="flex items-center">
              <input
                type="number"
                step="0.01"
                min="0"
                value={taxAmount}
                onChange={(e) => onTaxChange(Number(e.target.value))}
                className="w-24 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-primary-500 ml-2"
              />
              <span className="text-sm text-gray-600">ر.س</span>
            </div>
          </div>

          {/* Total */}
          <div className="flex justify-between items-center text-lg font-semibold border-t border-gray-200 pt-4">
            <span className="text-gray-900">الإجمالي:</span>
            <span className="text-primary-600">
              {total.toLocaleString()} ر.س
            </span>
          </div>
        </div>
      )}

      {/* Payment Method */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <div className="flex items-center mb-4">
          <CreditCard className="h-4 w-4 text-gray-600 ml-2" />
          <label className="text-sm font-medium text-gray-700">طريقة الدفع</label>
        </div>
        <select
          value={paymentMethod}
          onChange={(e) => onPaymentMethodChange(e.target.value as PaymentMethod)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
        >
          {paymentMethodOptions.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>

      {/* Notes */}
      <div className="mt-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          ملاحظات
        </label>
        <textarea
          value={notes}
          onChange={(e) => onNotesChange(e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          placeholder="أي ملاحظات إضافية..."
        />
      </div>
    </div>
  )
}
