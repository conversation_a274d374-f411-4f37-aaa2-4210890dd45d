// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// User Management
model User {
  id          String   @id @default(cuid())
  username    String   @unique
  email       String   @unique
  password    String
  fullName    String
  role        String
  permissions String // JSON string of permissions array
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  createdInvoices    Invoice[]       @relation("InvoiceCreatedBy")
  createdExpenses    Expense[]       @relation("ExpenseCreatedBy")
  createdRevenues    Revenue[]       @relation("RevenueCreatedBy")
  createdReceipts    Receipt[]       @relation("ReceiptCreatedBy")
  createdMovements   StockMovement[] @relation("MovementCreatedBy")

  @@map("users")
}

// Enums replaced with String for SQLite compatibility
// UserRole: ADMIN, SALES_MANAGER, SALES_EMPLOYEE, WAREHOUSE_MANAGER, WAREHOUSE_EMPLOYEE, ACCOUNTANT, CASHIER
// Permission: MANAGE_USERS, VIEW_USERS, MANAGE_INVENTORY, VIEW_INVENTORY, ADD_PRODUCTS, EDIT_PRODUCTS, DELETE_PRODUCTS, MANAGE_SALES, VIEW_SALES, CREATE_INVOICES, EDIT_INVOICES, DELETE_INVOICES, PRINT_INVOICES, MANAGE_EXPENSES, MANAGE_REVENUES, MANAGE_RECEIPTS, MANAGE_PAYMENTS, VIEW_FINANCIAL_REPORTS, VIEW_REPORTS, EXPORT_REPORTS, MANAGE_COMPANY_SETTINGS, MANAGE_SUPPLIERS, VIEW_SUPPLIERS

// Company Settings
model Company {
  id                 String   @id @default(cuid())
  name               String
  nameEn             String?
  logo               String?
  address            String
  phone              String
  email              String
  website            String?
  taxNumber          String?
  commercialRegister String?
  currency           String   @default("SAR")
  invoicePrefix      String   @default("INV")
  receiptPrefix      String   @default("REC")
  paymentPrefix      String   @default("PAY")
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  @@map("companies")
}

// Product Management
model ProductCategory {
  id          String    @id @default(cuid())
  name        String
  nameEn      String?
  description String?
  parentId    String?
  parent      ProductCategory? @relation("CategoryParent", fields: [parentId], references: [id])
  children    ProductCategory[] @relation("CategoryParent")
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  products Product[]

  @@map("product_categories")
}

model Product {
  id           String          @id @default(cuid())
  name         String
  nameEn       String?
  description  String?
  sku          String          @unique
  barcode      String?         @unique
  categoryId   String
  category     ProductCategory @relation(fields: [categoryId], references: [id])
  unit         String
  costPrice    Float
  sellingPrice Float
  minStock     Int             @default(0)
  currentStock Int             @default(0)
  isActive     Boolean         @default(true)
  createdAt    DateTime        @default(now())
  updatedAt    DateTime        @updatedAt

  // Relations
  invoiceItems  InvoiceItem[]
  stockMovements StockMovement[]

  @@map("products")
}

// Customer Management
model Customer {
  id            String    @id @default(cuid())
  name          String
  phone         String?
  email         String?
  address       String?
  taxNumber     String?
  creditLimit   Float   @default(0)
  currentBalance Float  @default(0)
  isActive      Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  invoices Invoice[]

  @@map("customers")
}

// Supplier Management
model Supplier {
  id             String   @id @default(cuid())
  name           String
  phone          String?
  email          String?
  address        String?
  taxNumber      String?
  currentBalance Float  @default(0)
  isActive       Boolean  @default(true)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@map("suppliers")
}

// Invoice Management
model Invoice {
  id              String        @id @default(cuid())
  invoiceNumber   String        @unique
  customerId      String
  customer        Customer      @relation(fields: [customerId], references: [id])
  items           InvoiceItem[]
  subtotal        Float
  taxAmount       Float       @default(0)
  discountAmount  Float       @default(0)
  total           Float
  paidAmount      Float       @default(0)
  remainingAmount Float
  status          String @default("PENDING")
  paymentMethod   String
  notes           String?
  createdBy       String
  createdByUser   User          @relation("InvoiceCreatedBy", fields: [createdBy], references: [id])
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  receipts Receipt[]

  @@map("invoices")
}

model InvoiceItem {
  id        String  @id @default(cuid())
  invoiceId String
  invoice   Invoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  productId String
  product   Product @relation(fields: [productId], references: [id])
  quantity  Int
  unitPrice Float
  discount  Float @default(0)
  total     Float

  @@map("invoice_items")
}

// InvoiceStatus: DRAFT, PENDING, PAID, PARTIALLY_PAID, CANCELLED, RETURNED
// PaymentMethod: CASH, CREDIT_CARD, BANK_TRANSFER, CHECK, CREDIT

// Financial Management
model ExpenseCategory {
  id          String    @id @default(cuid())
  name        String
  description String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  expenses Expense[]

  @@map("expense_categories")
}

model Expense {
  id            String          @id @default(cuid())
  description   String
  amount        Float
  categoryId    String
  category      ExpenseCategory @relation(fields: [categoryId], references: [id])
  date          DateTime
  paymentMethod String
  receiptNumber String?
  notes         String?
  createdBy     String
  createdByUser User            @relation("ExpenseCreatedBy", fields: [createdBy], references: [id])
  createdAt     DateTime        @default(now())

  @@map("expenses")
}

model RevenueCategory {
  id          String    @id @default(cuid())
  name        String
  description String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  revenues Revenue[]

  @@map("revenue_categories")
}

model Revenue {
  id            String          @id @default(cuid())
  description   String
  amount        Float
  categoryId    String
  category      RevenueCategory @relation(fields: [categoryId], references: [id])
  date          DateTime
  paymentMethod String
  receiptNumber String?
  notes         String?
  createdBy     String
  createdByUser User            @relation("RevenueCreatedBy", fields: [createdBy], references: [id])
  createdAt     DateTime        @default(now())

  @@map("revenues")
}

// Receipt Management
model Receipt {
  id               String        @id @default(cuid())
  receiptNumber    String        @unique
  type             String
  amount           Float
  fromTo           String
  description      String
  date             DateTime
  paymentMethod    String
  relatedInvoiceId String?
  relatedInvoice   Invoice?      @relation(fields: [relatedInvoiceId], references: [id])
  notes            String?
  createdBy        String
  createdByUser    User          @relation("ReceiptCreatedBy", fields: [createdBy], references: [id])
  createdAt        DateTime      @default(now())

  @@map("receipts")
}

// ReceiptType: RECEIPT (سند قبض), PAYMENT (سند دفع)

// Stock Management
model StockMovement {
  id              String       @id @default(cuid())
  productId       String
  product         Product      @relation(fields: [productId], references: [id])
  type            String
  quantity        Int
  unitCost        Float?
  totalCost       Float?
  reason          String
  referenceNumber String?
  date            DateTime
  createdBy       String
  createdByUser   User         @relation("MovementCreatedBy", fields: [createdBy], references: [id])
  createdAt       DateTime     @default(now())

  @@map("stock_movements")
}

// MovementType: IN (دخول), OUT (خروج), ADJUSTMENT (تسوية)
