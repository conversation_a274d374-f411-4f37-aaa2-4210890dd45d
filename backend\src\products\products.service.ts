import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';

@Injectable()
export class ProductsService {
  constructor(private prisma: PrismaService) {}

  async create(createProductDto: CreateProductDto) {
    // التحقق من وجود SKU مكرر
    const existingSku = await this.prisma.product.findUnique({
      where: { sku: createProductDto.sku },
    });

    if (existingSku) {
      throw new ConflictException('رمز المنتج (SKU) موجود بالفعل');
    }

    // التحقق من وجود باركود مكرر إذا تم توفيره
    if (createProductDto.barcode) {
      const existingBarcode = await this.prisma.product.findUnique({
        where: { barcode: createProductDto.barcode },
      });

      if (existingBarcode) {
        throw new ConflictException('الباركود موجود بالفعل');
      }
    }

    // التحقق من وجود الفئة
    const category = await this.prisma.productCategory.findUnique({
      where: { id: createProductDto.categoryId },
    });

    if (!category) {
      throw new NotFoundException('فئة المنتج غير موجودة');
    }

    return this.prisma.product.create({
      data: createProductDto,
      include: {
        category: true,
      },
    });
  }

  async findAll(categoryId?: string, isActive?: boolean) {
    const where: any = {};

    if (categoryId) {
      where.categoryId = categoryId;
    }

    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    return this.prisma.product.findMany({
      where,
      include: {
        category: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async findOne(id: string) {
    const product = await this.prisma.product.findUnique({
      where: { id },
      include: {
        category: true,
        stockMovements: {
          orderBy: {
            createdAt: 'desc',
          },
          take: 10,
        },
      },
    });

    if (!product) {
      throw new NotFoundException('المنتج غير موجود');
    }

    return product;
  }

  async findBySku(sku: string) {
    return this.prisma.product.findUnique({
      where: { sku },
      include: {
        category: true,
      },
    });
  }

  async findByBarcode(barcode: string) {
    return this.prisma.product.findUnique({
      where: { barcode },
      include: {
        category: true,
      },
    });
  }

  async update(id: string, updateProductDto: UpdateProductDto) {
    const product = await this.findOne(id);

    // التحقق من SKU إذا تم تغييره
    if (updateProductDto.sku && updateProductDto.sku !== product.sku) {
      const existingSku = await this.findBySku(updateProductDto.sku);
      if (existingSku) {
        throw new ConflictException('رمز المنتج (SKU) موجود بالفعل');
      }
    }

    // التحقق من الباركود إذا تم تغييره
    if (updateProductDto.barcode && updateProductDto.barcode !== product.barcode) {
      const existingBarcode = await this.findByBarcode(updateProductDto.barcode);
      if (existingBarcode) {
        throw new ConflictException('الباركود موجود بالفعل');
      }
    }

    // التحقق من الفئة إذا تم تغييرها
    if (updateProductDto.categoryId && updateProductDto.categoryId !== product.categoryId) {
      const category = await this.prisma.productCategory.findUnique({
        where: { id: updateProductDto.categoryId },
      });

      if (!category) {
        throw new NotFoundException('فئة المنتج غير موجودة');
      }
    }

    return this.prisma.product.update({
      where: { id },
      data: updateProductDto,
      include: {
        category: true,
      },
    });
  }

  async remove(id: string) {
    await this.findOne(id);

    return this.prisma.product.delete({
      where: { id },
    });
  }

  async toggleActive(id: string) {
    const product = await this.findOne(id);

    return this.prisma.product.update({
      where: { id },
      data: { isActive: !product.isActive },
      include: {
        category: true,
      },
    });
  }

  async updateStock(id: string, newStock: number) {
    await this.findOne(id);

    return this.prisma.product.update({
      where: { id },
      data: { currentStock: newStock },
      include: {
        category: true,
      },
    });
  }

  async getLowStockProducts() {
    return this.prisma.$queryRaw`
      SELECT p.*, c.name as categoryName
      FROM products p
      LEFT JOIN product_categories c ON p.categoryId = c.id
      WHERE p.isActive = 1 AND p.currentStock <= p.minStock
      ORDER BY p.currentStock ASC
    `;
  }
}
