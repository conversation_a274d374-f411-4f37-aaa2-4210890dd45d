'use client'

import { useState } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { ProductForm } from '@/components/inventory/product-form'
import { ProductList } from '@/components/inventory/product-list'
import { ProductStats } from '@/components/inventory/product-stats'
import { Plus, Filter, Download, Search, Package } from 'lucide-react'
import { Product } from '@/types'

export default function ProductsPage() {
  const [showForm, setShowForm] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [stockFilter, setStockFilter] = useState('all')

  // Mock data - في التطبيق الحقيقي، ستأتي من API
  const products: Product[] = [
    {
      id: '1',
      name: 'لا<PERSON>توب ديل XPS 13',
      nameEn: 'Dell XPS 13 Laptop',
      description: 'لابتوب عالي الأداء للأعمال والتصميم',
      sku: 'DELL-XPS-13-001',
      barcode: '1234567890123',
      category: {
        id: '1',
        name: 'أجهزة الكمبيوتر',
        nameEn: 'Computers',
        isActive: true
      },
      unit: 'قطعة',
      costPrice: 3500,
      sellingPrice: 4200,
      minStock: 5,
      currentStock: 12,
      isActive: true,
      createdAt: new Date('2024-01-10'),
      updatedAt: new Date('2024-01-15')
    },
    {
      id: '2',
      name: 'ماوس لوجيتك MX Master 3',
      nameEn: 'Logitech MX Master 3 Mouse',
      description: 'ماوس لاسلكي متقدم للمحترفين',
      sku: 'LOG-MX3-001',
      barcode: '1234567890124',
      category: {
        id: '2',
        name: 'ملحقات الكمبيوتر',
        nameEn: 'Computer Accessories',
        isActive: true
      },
      unit: 'قطعة',
      costPrice: 280,
      sellingPrice: 350,
      minStock: 10,
      currentStock: 3,
      isActive: true,
      createdAt: new Date('2024-01-12'),
      updatedAt: new Date('2024-01-15')
    },
    {
      id: '3',
      name: 'شاشة سامسونج 27 بوصة',
      nameEn: 'Samsung 27" Monitor',
      description: 'شاشة 4K عالية الدقة للألعاب والتصميم',
      sku: 'SAM-MON-27-001',
      barcode: '1234567890125',
      category: {
        id: '3',
        name: 'الشاشات',
        nameEn: 'Monitors',
        isActive: true
      },
      unit: 'قطعة',
      costPrice: 1200,
      sellingPrice: 1500,
      minStock: 3,
      currentStock: 0,
      isActive: true,
      createdAt: new Date('2024-01-08'),
      updatedAt: new Date('2024-01-15')
    },
    {
      id: '4',
      name: 'كيبورد ميكانيكي',
      nameEn: 'Mechanical Keyboard',
      description: 'كيبورد ميكانيكي للألعاب مع إضاءة RGB',
      sku: 'MECH-KB-001',
      barcode: '1234567890126',
      category: {
        id: '2',
        name: 'ملحقات الكمبيوتر',
        nameEn: 'Computer Accessories',
        isActive: true
      },
      unit: 'قطعة',
      costPrice: 450,
      sellingPrice: 580,
      minStock: 8,
      currentStock: 6,
      isActive: true,
      createdAt: new Date('2024-01-14'),
      updatedAt: new Date('2024-01-15')
    }
  ]

  const categories = [
    { id: '1', name: 'أجهزة الكمبيوتر' },
    { id: '2', name: 'ملحقات الكمبيوتر' },
    { id: '3', name: 'الشاشات' },
  ]

  const handleAddProduct = () => {
    setSelectedProduct(null)
    setShowForm(true)
  }

  const handleEditProduct = (product: Product) => {
    setSelectedProduct(product)
    setShowForm(true)
  }

  const handleCloseForm = () => {
    setShowForm(false)
    setSelectedProduct(null)
  }

  const handleSaveProduct = (productData: any) => {
    // هنا سيتم حفظ البيانات في API
    console.log('Saving product:', productData)
    handleCloseForm()
  }

  const filteredProducts = products.filter(product => {
    // فلترة حسب البحث
    const matchesSearch = searchQuery === '' || 
      product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.sku.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (product.barcode && product.barcode.includes(searchQuery))

    // فلترة حسب الفئة
    const matchesCategory = categoryFilter === 'all' || product.category.id === categoryFilter

    // فلترة حسب المخزون
    let matchesStock = true
    if (stockFilter === 'low') {
      matchesStock = product.currentStock <= product.minStock && product.currentStock > 0
    } else if (stockFilter === 'out') {
      matchesStock = product.currentStock === 0
    } else if (stockFilter === 'available') {
      matchesStock = product.currentStock > product.minStock
    }

    return matchesSearch && matchesCategory && matchesStock
  })

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة المنتجات</h1>
            <p className="text-gray-600 mt-1">إدارة جميع منتجات المخزن</p>
          </div>
          <div className="flex items-center space-x-3 space-x-reverse">
            <button
              onClick={handleAddProduct}
              className="btn-primary flex items-center"
            >
              <Plus className="h-4 w-4 ml-2" />
              منتج جديد
            </button>
            <button className="btn-secondary flex items-center">
              <Download className="h-4 w-4 ml-2" />
              تصدير
            </button>
          </div>
        </div>

        {/* Stats */}
        <ProductStats products={products} />

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-md border border-gray-200 p-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 sm:space-x-reverse">
              {/* Search */}
              <div className="relative">
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="block w-full pr-10 pl-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="البحث بالاسم أو SKU أو الباركود..."
                />
              </div>

              {/* Category Filter */}
              <div className="flex items-center">
                <label className="text-sm font-medium text-gray-700 ml-2">الفئة:</label>
                <select
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value)}
                  className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="all">جميع الفئات</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Stock Filter */}
              <div className="flex items-center">
                <label className="text-sm font-medium text-gray-700 ml-2">المخزون:</label>
                <select
                  value={stockFilter}
                  onChange={(e) => setStockFilter(e.target.value)}
                  className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="all">جميع المنتجات</option>
                  <option value="available">متوفر</option>
                  <option value="low">مخزون منخفض</option>
                  <option value="out">نفد المخزون</option>
                </select>
              </div>

              <button className="btn-secondary flex items-center text-sm">
                <Filter className="h-4 w-4 ml-2" />
                المزيد من الفلاتر
              </button>
            </div>

            <div className="text-sm text-gray-600">
              عرض {filteredProducts.length} من {products.length} منتج
            </div>
          </div>
        </div>

        {/* Product List */}
        <ProductList
          products={filteredProducts}
          onEdit={handleEditProduct}
          onDelete={(id) => console.log('Delete product:', id)}
          onViewStock={(id) => console.log('View stock movements:', id)}
        />

        {/* Product Form Modal */}
        {showForm && (
          <ProductForm
            product={selectedProduct}
            categories={categories}
            onSave={handleSaveProduct}
            onClose={handleCloseForm}
          />
        )}
      </div>
    </DashboardLayout>
  )
}
