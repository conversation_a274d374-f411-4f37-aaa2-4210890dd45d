import { Customer, Product, InvoiceItemForm, PaymentMethod } from '@/types'
import { X, Printer, Download, Building2 } from 'lucide-react'

interface InvoicePreviewProps {
  customer: Customer
  items: InvoiceItemForm[]
  products: Product[]
  subtotal: number
  taxAmount: number
  discountAmount: number
  total: number
  paymentMethod: PaymentMethod
  notes?: string
  onClose: () => void
  onPrint: () => void
}

const paymentMethodLabels: Record<PaymentMethod, string> = {
  CASH: 'نقدي',
  CREDIT_CARD: 'بطاقة ائتمان',
  BANK_TRANSFER: 'تحويل بنكي',
  CHECK: 'شيك',
  CREDIT: 'آجل'
}

export function InvoicePreview({
  customer,
  items,
  products,
  subtotal,
  taxAmount,
  discountAmount,
  total,
  paymentMethod,
  notes,
  onClose,
  onPrint
}: InvoicePreviewProps) {
  const getProductById = (productId: string) => {
    return products.find(p => p.id === productId)
  }

  const calculateItemTotal = (item: InvoiceItemForm) => {
    return (item.quantity * item.unitPrice) - item.discount
  }

  const invoiceNumber = `INV-${Date.now().toString().slice(-6)}`
  const currentDate = new Date().toLocaleDateString('ar-SA')

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 no-print">
          <h2 className="text-xl font-semibold text-gray-900">معاينة الفاتورة</h2>
          <div className="flex items-center space-x-3 space-x-reverse">
            <button
              onClick={onPrint}
              className="btn-primary flex items-center"
            >
              <Printer className="h-4 w-4 ml-2" />
              طباعة
            </button>
            <button
              onClick={() => console.log('Download PDF')}
              className="btn-secondary flex items-center"
            >
              <Download className="h-4 w-4 ml-2" />
              تحميل PDF
            </button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 p-2 rounded-lg hover:bg-gray-100"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Invoice Content */}
        <div className="p-8" id="invoice-content">
          {/* Company Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center">
              <div className="h-16 w-16 bg-primary-600 rounded-lg flex items-center justify-center ml-4">
                <Building2 className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">شركة المثال للتجارة</h1>
                <p className="text-gray-600">الرياض، المملكة العربية السعودية</p>
                <p className="text-gray-600">هاتف: +966501234567 | البريد: <EMAIL></p>
              </div>
            </div>
            <div className="text-left">
              <h2 className="text-3xl font-bold text-primary-600 mb-2">فاتورة مبيعات</h2>
              <div className="text-gray-600">
                <p>رقم الفاتورة: <span className="font-semibold">{invoiceNumber}</span></p>
                <p>التاريخ: <span className="font-semibold">{currentDate}</span></p>
              </div>
            </div>
          </div>

          {/* Customer Info */}
          <div className="bg-gray-50 rounded-lg p-6 mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">بيانات العميل</h3>
            <div className="grid grid-cols-2 gap-6">
              <div>
                <p className="text-gray-600">الاسم:</p>
                <p className="font-semibold text-gray-900">{customer.name}</p>
              </div>
              {customer.phone && (
                <div>
                  <p className="text-gray-600">الهاتف:</p>
                  <p className="font-semibold text-gray-900">{customer.phone}</p>
                </div>
              )}
              {customer.email && (
                <div>
                  <p className="text-gray-600">البريد الإلكتروني:</p>
                  <p className="font-semibold text-gray-900">{customer.email}</p>
                </div>
              )}
              {customer.address && (
                <div>
                  <p className="text-gray-600">العنوان:</p>
                  <p className="font-semibold text-gray-900">{customer.address}</p>
                </div>
              )}
              {customer.taxNumber && (
                <div>
                  <p className="text-gray-600">الرقم الضريبي:</p>
                  <p className="font-semibold text-gray-900">{customer.taxNumber}</p>
                </div>
              )}
              <div>
                <p className="text-gray-600">طريقة الدفع:</p>
                <p className="font-semibold text-gray-900">{paymentMethodLabels[paymentMethod]}</p>
              </div>
            </div>
          </div>

          {/* Invoice Items */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">تفاصيل الفاتورة</h3>
            <div className="overflow-x-auto">
              <table className="w-full border border-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-right text-sm font-semibold text-gray-900 border-b border-gray-200">
                      المنتج
                    </th>
                    <th className="px-4 py-3 text-center text-sm font-semibold text-gray-900 border-b border-gray-200">
                      الكمية
                    </th>
                    <th className="px-4 py-3 text-center text-sm font-semibold text-gray-900 border-b border-gray-200">
                      سعر الوحدة
                    </th>
                    <th className="px-4 py-3 text-center text-sm font-semibold text-gray-900 border-b border-gray-200">
                      الخصم
                    </th>
                    <th className="px-4 py-3 text-center text-sm font-semibold text-gray-900 border-b border-gray-200">
                      الإجمالي
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {items.map((item, index) => {
                    const product = getProductById(item.productId)
                    if (!product) return null

                    return (
                      <tr key={item.productId} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                        <td className="px-4 py-3 border-b border-gray-200">
                          <div>
                            <p className="font-medium text-gray-900">{product.name}</p>
                            <p className="text-sm text-gray-600">SKU: {product.sku}</p>
                          </div>
                        </td>
                        <td className="px-4 py-3 text-center border-b border-gray-200">
                          {item.quantity} {product.unit}
                        </td>
                        <td className="px-4 py-3 text-center border-b border-gray-200">
                          {item.unitPrice.toLocaleString()} ر.س
                        </td>
                        <td className="px-4 py-3 text-center border-b border-gray-200">
                          {item.discount.toLocaleString()} ر.س
                        </td>
                        <td className="px-4 py-3 text-center font-semibold border-b border-gray-200">
                          {calculateItemTotal(item).toLocaleString()} ر.س
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          </div>

          {/* Invoice Totals */}
          <div className="flex justify-end mb-8">
            <div className="w-80">
              <div className="bg-gray-50 rounded-lg p-6">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">المجموع الفرعي:</span>
                    <span className="font-medium">{subtotal.toLocaleString()} ر.س</span>
                  </div>
                  {discountAmount > 0 && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">الخصم:</span>
                      <span className="font-medium text-red-600">-{discountAmount.toLocaleString()} ر.س</span>
                    </div>
                  )}
                  {taxAmount > 0 && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">الضريبة:</span>
                      <span className="font-medium">{taxAmount.toLocaleString()} ر.س</span>
                    </div>
                  )}
                  <div className="border-t border-gray-300 pt-3">
                    <div className="flex justify-between text-lg font-bold">
                      <span className="text-gray-900">الإجمالي:</span>
                      <span className="text-primary-600">{total.toLocaleString()} ر.س</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Notes */}
          {notes && (
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">ملاحظات</h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-gray-700">{notes}</p>
              </div>
            </div>
          )}

          {/* Footer */}
          <div className="border-t border-gray-200 pt-6">
            <div className="grid grid-cols-2 gap-8">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">شروط الدفع</h4>
                <p className="text-sm text-gray-600">
                  يرجى الدفع خلال 30 يوماً من تاريخ الفاتورة
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">معلومات التواصل</h4>
                <p className="text-sm text-gray-600">
                  للاستفسارات: +966501234567<br />
                  البريد الإلكتروني: <EMAIL>
                </p>
              </div>
            </div>
            <div className="text-center mt-6 pt-6 border-t border-gray-200">
              <p className="text-sm text-gray-500">
                شكراً لتعاملكم معنا
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
