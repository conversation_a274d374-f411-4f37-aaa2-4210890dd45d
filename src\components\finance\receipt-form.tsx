'use client'

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Receipt, ReceiptForm as ReceiptFormType, ReceiptType, PaymentMethod } from '@/types'
import { X, Save, Receipt as ReceiptIcon, CreditCard } from 'lucide-react'
import { useState } from 'react'
import toast from 'react-hot-toast'

const receiptSchema = z.object({
  type: z.nativeEnum(ReceiptType),
  amount: z.number().min(0.01, 'المبلغ يجب أن يكون أكبر من صفر'),
  fromTo: z.string().min(1, 'اسم الشخص أو الجهة مطلوب'),
  description: z.string().min(1, 'وصف السند مطلوب'),
  date: z.string().min(1, 'التاريخ مطلوب'),
  paymentMethod: z.nativeEnum(PaymentMethod),
  relatedInvoiceId: z.string().optional(),
  notes: z.string().optional(),
})

interface ReceiptFormProps {
  receipt?: Receipt | null
  onSave: (data: ReceiptFormType) => void
  onClose: () => void
}

const paymentMethodOptions = [
  { value: PaymentMethod.CASH, label: 'نقدي' },
  { value: PaymentMethod.CREDIT_CARD, label: 'بطاقة ائتمان' },
  { value: PaymentMethod.BANK_TRANSFER, label: 'تحويل بنكي' },
  { value: PaymentMethod.CHECK, label: 'شيك' },
]

export function ReceiptForm({ receipt, onSave, onClose }: ReceiptFormProps) {
  const [isLoading, setIsLoading] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<ReceiptFormType>({
    resolver: zodResolver(receiptSchema),
    defaultValues: receipt ? {
      type: receipt.type,
      amount: receipt.amount,
      fromTo: receipt.fromTo,
      description: receipt.description,
      date: new Date(receipt.date).toISOString().split('T')[0],
      paymentMethod: receipt.paymentMethod,
      relatedInvoiceId: receipt.relatedInvoiceId || '',
      notes: receipt.notes || '',
    } : {
      type: ReceiptType.RECEIPT,
      date: new Date().toISOString().split('T')[0],
      paymentMethod: PaymentMethod.CASH,
    }
  })

  const receiptType = watch('type')

  const onSubmit = async (data: ReceiptFormType) => {
    try {
      setIsLoading(true)
      
      // تحويل التاريخ إلى Date object
      const formData = {
        ...data,
        date: new Date(data.date),
        amount: Number(data.amount)
      }
      
      await onSave(formData)
      toast.success(receipt ? 'تم تحديث السند بنجاح' : 'تم إنشاء السند بنجاح')
    } catch (error) {
      toast.error('حدث خطأ أثناء حفظ البيانات')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            {receiptType === ReceiptType.RECEIPT ? (
              <ReceiptIcon className="h-6 w-6 text-green-600 ml-3" />
            ) : (
              <CreditCard className="h-6 w-6 text-red-600 ml-3" />
            )}
            <h2 className="text-xl font-semibold text-gray-900">
              {receipt 
                ? (receiptType === ReceiptType.RECEIPT ? 'تعديل سند قبض' : 'تعديل سند دفع')
                : (receiptType === ReceiptType.RECEIPT ? 'سند قبض جديد' : 'سند دفع جديد')
              }
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 p-2 rounded-lg hover:bg-gray-100"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                نوع السند *
              </label>
              <select {...register('type')} className="input-field">
                <option value={ReceiptType.RECEIPT}>سند قبض</option>
                <option value={ReceiptType.PAYMENT}>سند دفع</option>
              </select>
              {errors.type && (
                <p className="mt-1 text-sm text-red-600">{errors.type.message}</p>
              )}
            </div>

            {/* Amount */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                المبلغ (ر.س) *
              </label>
              <input
                {...register('amount', { valueAsNumber: true })}
                type="number"
                step="0.01"
                min="0"
                className="input-field"
                placeholder="0.00"
              />
              {errors.amount && (
                <p className="mt-1 text-sm text-red-600">{errors.amount.message}</p>
              )}
            </div>

            {/* From/To */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {receiptType === ReceiptType.RECEIPT ? 'مستلم من *' : 'مدفوع إلى *'}
              </label>
              <input
                {...register('fromTo')}
                type="text"
                className="input-field"
                placeholder={receiptType === ReceiptType.RECEIPT ? 'اسم العميل أو الجهة' : 'اسم المورد أو الجهة'}
              />
              {errors.fromTo && (
                <p className="mt-1 text-sm text-red-600">{errors.fromTo.message}</p>
              )}
            </div>

            {/* Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                التاريخ *
              </label>
              <input
                {...register('date')}
                type="date"
                className="input-field"
              />
              {errors.date && (
                <p className="mt-1 text-sm text-red-600">{errors.date.message}</p>
              )}
            </div>

            {/* Payment Method */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                طريقة الدفع *
              </label>
              <select {...register('paymentMethod')} className="input-field">
                {paymentMethodOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.paymentMethod && (
                <p className="mt-1 text-sm text-red-600">{errors.paymentMethod.message}</p>
              )}
            </div>

            {/* Related Invoice */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                رقم الفاتورة المرتبطة
              </label>
              <input
                {...register('relatedInvoiceId')}
                type="text"
                className="input-field"
                placeholder="INV-001 (اختياري)"
              />
            </div>

            {/* Description */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                وصف السند *
              </label>
              <input
                {...register('description')}
                type="text"
                className="input-field"
                placeholder={receiptType === ReceiptType.RECEIPT 
                  ? 'مثال: دفعة من فاتورة، دفع مقدم، إيراد إضافي' 
                  : 'مثال: دفع فاتورة، راتب موظف، مصروف تشغيلي'
                }
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
              )}
            </div>

            {/* Notes */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                ملاحظات
              </label>
              <textarea
                {...register('notes')}
                rows={3}
                className="input-field"
                placeholder="أي ملاحظات إضافية..."
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3 space-x-reverse pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary"
            >
              إلغاء
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className={`flex items-center font-medium py-2 px-4 rounded-lg transition-colors duration-200 ${
                receiptType === ReceiptType.RECEIPT
                  ? 'bg-green-600 hover:bg-green-700 text-white'
                  : 'bg-red-600 hover:bg-red-700 text-white'
              }`}
            >
              {isLoading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
              ) : (
                <Save className="h-4 w-4 ml-2" />
              )}
              {isLoading ? 'جاري الحفظ...' : (receipt ? 'تحديث' : 'حفظ')}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
