import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { ProductsService } from './products.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('المنتجات')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('products')
export class ProductsController {
  constructor(private readonly productsService: ProductsService) {}

  @ApiOperation({ summary: 'إنشاء منتج جديد' })
  @ApiResponse({ status: 201, description: 'تم إنشاء المنتج بنجاح' })
  @ApiResponse({ status: 409, description: 'رمز المنتج أو الباركود موجود بالفعل' })
  @Post()
  create(@Body() createProductDto: CreateProductDto) {
    return this.productsService.create(createProductDto);
  }

  @ApiOperation({ summary: 'الحصول على جميع المنتجات' })
  @ApiResponse({ status: 200, description: 'قائمة المنتجات' })
  @ApiQuery({ name: 'categoryId', required: false, description: 'معرف الفئة' })
  @ApiQuery({ name: 'isActive', required: false, description: 'حالة التفعيل' })
  @Get()
  findAll(
    @Query('categoryId') categoryId?: string,
    @Query('isActive') isActive?: string,
  ) {
    const isActiveBoolean = isActive === 'true' ? true : isActive === 'false' ? false : undefined;
    return this.productsService.findAll(categoryId, isActiveBoolean);
  }

  @ApiOperation({ summary: 'الحصول على المنتجات منخفضة المخزون' })
  @ApiResponse({ status: 200, description: 'قائمة المنتجات منخفضة المخزون' })
  @Get('low-stock')
  getLowStockProducts() {
    return this.productsService.getLowStockProducts();
  }

  @ApiOperation({ summary: 'البحث عن منتج بالباركود' })
  @ApiResponse({ status: 200, description: 'بيانات المنتج' })
  @ApiResponse({ status: 404, description: 'المنتج غير موجود' })
  @Get('barcode/:barcode')
  findByBarcode(@Param('barcode') barcode: string) {
    return this.productsService.findByBarcode(barcode);
  }

  @ApiOperation({ summary: 'البحث عن منتج بـ SKU' })
  @ApiResponse({ status: 200, description: 'بيانات المنتج' })
  @ApiResponse({ status: 404, description: 'المنتج غير موجود' })
  @Get('sku/:sku')
  findBySku(@Param('sku') sku: string) {
    return this.productsService.findBySku(sku);
  }

  @ApiOperation({ summary: 'الحصول على منتج بالمعرف' })
  @ApiResponse({ status: 200, description: 'بيانات المنتج' })
  @ApiResponse({ status: 404, description: 'المنتج غير موجود' })
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.productsService.findOne(id);
  }

  @ApiOperation({ summary: 'تحديث بيانات المنتج' })
  @ApiResponse({ status: 200, description: 'تم تحديث المنتج بنجاح' })
  @ApiResponse({ status: 404, description: 'المنتج غير موجود' })
  @Patch(':id')
  update(@Param('id') id: string, @Body() updateProductDto: UpdateProductDto) {
    return this.productsService.update(id, updateProductDto);
  }

  @ApiOperation({ summary: 'تحديث مخزون المنتج' })
  @ApiResponse({ status: 200, description: 'تم تحديث المخزون بنجاح' })
  @ApiResponse({ status: 404, description: 'المنتج غير موجود' })
  @Patch(':id/stock')
  updateStock(@Param('id') id: string, @Body('stock') stock: number) {
    return this.productsService.updateStock(id, stock);
  }

  @ApiOperation({ summary: 'تفعيل/إلغاء تفعيل المنتج' })
  @ApiResponse({ status: 200, description: 'تم تغيير حالة المنتج بنجاح' })
  @ApiResponse({ status: 404, description: 'المنتج غير موجود' })
  @Patch(':id/toggle-active')
  toggleActive(@Param('id') id: string) {
    return this.productsService.toggleActive(id);
  }

  @ApiOperation({ summary: 'حذف المنتج' })
  @ApiResponse({ status: 200, description: 'تم حذف المنتج بنجاح' })
  @ApiResponse({ status: 404, description: 'المنتج غير موجود' })
  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.productsService.remove(id);
  }
}
